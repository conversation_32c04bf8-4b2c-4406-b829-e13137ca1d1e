import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/approval_terminasi_model.dart';
import 'package:pdl_superapp/models/body/approval_body.dart';
import 'package:pdl_superapp/models/termination_candidate_model.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:pdl_superapp/utils/keys.dart';

class ApprovalTerminasiController extends BaseControllers {
  final TextEditingController alasanController = TextEditingController();
  final RxInt terminationId = 0.obs;
  final RxString terminationStatus = ''.obs;
  final RxString agentName = ''.obs;
  final RxString agentCode = ''.obs;
  final RxString level = ''.obs;
  final RxString reason = ''.obs;
  late SharedPreferences prefs;
  final RxBool hasChanges = false.obs;

  final RxBool needAssignPolicyTransfer = false.obs;

  final RxString policyTranferAssignedTo = ''.obs;
  final RxString policyTranferAssignedAgentCode = ''.obs;
  final RxList approvalDetails = [].obs;

  final Map<String, String> dropdownOptions = {};
  final RxString selectedValue = '0001'.obs;

  final TextEditingController polisTextController = TextEditingController();
  RxString selectedStatusApproval = ''.obs;
  TextEditingController remarksApproval = TextEditingController();
  final RxInt terminationApprovalHeaderId = 0.obs;

  @override
  void onInit() async {
    super.onInit();

    prefs = await SharedPreferences.getInstance();

    if (Get.arguments[kArgsTerminationData] != null) {
      ApprovalTerminasiModel terminationData =
          Get.arguments[kArgsTerminationData] ?? '';
      terminationApprovalHeaderId.value =
          Get.arguments[kArgsTerminationApprovalHeaderId] ?? 0;

      terminationStatus.value = terminationData.status ?? '';
      terminationId.value = terminationData.id ?? 0;
      agentCode.value = terminationData.target?.agentCode ?? '';
      agentName.value = terminationData.target?.name ?? '';
      level.value = terminationData.target?.agentLevel ?? '';

      reason.value = terminationData.reason ?? '';

      // policyTranferAssignedTo.value =
      //     terminationData.policyTransferInfo?.assignedTo ?? '';
      // policyTranferAssignedAgentCode.value =
      //     terminationData.policyTransferInfo?.assignedAgentCode ?? '';

      approvalDetails.value = terminationData.approvalDetails;
    }
    polisTextController.text = dropdownOptions[selectedValue.value] ?? '';
    getAgentList();

    remarksApproval.addListener(() {
      if (remarksApproval.text.isNotEmpty) {
        hasChanges.value = true;
      } else {
        hasChanges.value = false;
      }
    });
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    switch (requestCode) {
      case kReqGetEligibleCandidateTermination:
        dropdownOptions.clear();
        var datas = response['content'];
        for (int i = 0; i < datas.length; i++) {
          final terminasiData = TerminationCandidateModel.fromJson(datas[i]);
          dropdownOptions[terminasiData.agentCode ?? ''] =
              terminasiData.agentName ?? '';
        }
        break;
      case kReqSubmitPolicyTransfer:
        break;
      case kReqPostApproval:
        Get.back();
        break;
      case kReqCancelTermination:
        Get.back();
        Get.back();
      // Get.offNamed(
      //   Routes.TERMINASI_DETAIL,
      //   arguments: {
      //     kArgsTerminationReason: reasonTextController.text,
      //     kArgsSelfTermination: true,
      //   },
      // );
      // break;
      default:
    }
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    setLoading(false);
  }

  cancelTerminasi() async {
    var params = {"id": terminationId.value, "remarks": alasanController.text};

    setLoading(true);
    await api.cancelTermination(
      controllers: this,
      data: params,
      code: kReqCancelTermination,
    );
  }

  getAgentList() async {
    await api.getTerminationEligibleCandidates(
      controllers: this,
      params: null,
      code: kReqGetEligibleCandidateTermination,
    );
  }

  setHasChange() async {
    hasChanges.value = true;
  }

  submitPolicyTransfer(int trxId) async {
    var params = {
      "targetAgentCode": selectedValue.value,
      "trxTerminationId": trxId,
    };
    setLoading(true);
    await api.performSubmitPolicyTransfer(
      controllers: this,
      data: params,
      code: kReqSubmitPolicyTransfer,
    );
  }

  submitApprovalTerminasi({
    required int trxId,
    required ApprovalBody body,
  }) async {
    setLoading(true);
    if (needAssignPolicyTransfer.isTrue) {
      await submitPolicyTransfer(trxId);
    }
    await api.postApproval(
      controllers: this,
      code: kReqPostApproval,
      body: body,
    );
  }

  void refreshData() {}
}
