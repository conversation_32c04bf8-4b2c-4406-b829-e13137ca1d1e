# Background Upload System - Developer Guide

## 📋 Overview

Sistem Background Upload secara otomatis mendeteksi dan mengupload foto yang belum terupload ke server tanpa mengganggu user experience.

## ⏰ Kapan Foto Diupload?

### 1. **Upload Otomatis Berkala**
- **Interval**: Setiap 30 detik
- **Kondisi**: Hanya jika ada koneksi internet
- **Retry Logic**: Maksimal 3 attempts dengan cooldown 5 menit

### 2. **Upload Saat Kembali Online**
- **Trigger**: Ketika device kembali online setelah offline
- **Delay**: 2 detik setelah koneksi kembali
- **Behavior**: Langsung cek dan upload semua foto pending

### 3. **Upload Manual**
- **Trigger**: User menekan tombol save form
- **Behavior**: Upload semua foto pending sebelum submit form

## 🔧 Debug Mode

### Mengaktifkan Debug Mode

#### Cara 1: Melalui UI (Development Mode)
```dart
// Di form verification page (hanya tampil di debug mode)
// Tekan tombol "Enable Debug Notifications"
```

#### Cara 2: Programmatically
```dart
final service = Get.find<BackgroundUploadService>();
service.enableDebugMode(); // Aktifkan debug
service.disableDebugMode(); // Matikan debug
service.toggleDebugMode(); // Toggle debug
```

### Fitur Debug Mode

1. **Popup Notifications**
   - Network status changes (online/offline)
   - Upload progress dan hasil
   - Error notifications

2. **Debug Widget**
   - Real-time status monitoring
   - Pending photos count
   - Control buttons (force upload, pause/resume)

3. **Detailed Logging**
   - Console logs untuk semua aktivitas
   - Upload attempts tracking
   - Error details

## 📱 Cara Testing

### 1. Test Upload Otomatis
```dart
// 1. Ambil foto di form
// 2. Aktifkan debug mode
// 3. Tunggu 30 detik atau force upload
// 4. Lihat popup notification hasil upload
```

### 2. Test Offline/Online Scenario
```dart
// 1. Ambil foto saat online
// 2. Matikan internet/wifi
// 3. Lihat popup "Offline - Background upload paused"
// 4. Nyalakan kembali internet
// 5. Lihat popup "Online - Checking for pending photos..."
// 6. Foto akan diupload otomatis dalam 2 detik
```

### 3. Test Retry Mechanism
```dart
// 1. Ambil foto
// 2. Simulasikan network error (disconnect saat upload)
// 3. Service akan retry dengan cooldown 5 menit
// 4. Setelah 3 attempts gagal, foto dihapus dari pending
```

## 🎯 Status Indikator

### Visual Indicators
- **Badge "Local"**: Foto hanya tersedia di device, belum terupload
- **Progress Bar**: Saat foto sedang diupload
- **Success State**: Badge hilang setelah upload berhasil

### Debug Widget Status
- **Service Active**: Background service berjalan/tidak
- **Debug Mode**: Mode debug aktif/tidak
- **Currently Uploading**: Sedang upload/tidak
- **Total Pending**: Jumlah foto yang belum terupload
- **Uploaded Count**: Total foto yang sudah berhasil diupload

## 🚀 Integration Guide

### 1. Initialize Services
```dart
// Di main app initialization
import 'package:pdl_superapp/services/service_initializer.dart';

await ServiceInitializer.initializeServices();
```

### 2. Add Debug Widget (Development Only)
```dart
// Di page yang ingin ditambahkan debug widget
if (kDebugMode) {
  return Scaffold(
    body: YourPageContent(),
    floatingActionButton: FloatingActionButton(
      onPressed: () {
        Get.find<BackgroundUploadService>().toggleDebugMode();
      },
      child: Icon(Icons.bug_report),
    ),
  );
}
```

### 3. Monitor Upload Status
```dart
final service = Get.find<BackgroundUploadService>();

// Listen to upload status
service.isUploading.listen((isUploading) {
  print('Upload status: $isUploading');
});

// Check pending photos count
service.totalPendingPhotos.listen((count) {
  print('Pending photos: $count');
});
```

## ⚠️ Important Notes

1. **Web Platform**: Background upload tidak berjalan di web platform
2. **Battery Optimization**: Service berjalan di background, pastikan app tidak di-kill oleh system
3. **Network Changes**: Service otomatis pause saat offline dan resume saat online
4. **Memory Management**: Service menggunakan minimal memory dan CPU
5. **Error Handling**: Semua error di-handle gracefully tanpa crash app

## 🔍 Troubleshooting

### Foto Tidak Terupload
1. Cek koneksi internet
2. Cek apakah service aktif: `service.isBackgroundUploadActive`
3. Cek pending photos: `service.totalPendingPhotos.value`
4. Aktifkan debug mode untuk detail error

### Debug Mode Tidak Muncul
1. Pastikan app dalam debug mode (`kDebugMode = true`)
2. Pastikan service sudah diinisialisasi
3. Cek console untuk error messages

### Service Tidak Berjalan
1. Pastikan `ServiceInitializer.initializeServices()` dipanggil
2. Cek apakah ada error di console
3. Restart app jika perlu
