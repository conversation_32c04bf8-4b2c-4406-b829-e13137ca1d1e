import 'dart:async';
import 'dart:developer';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:no_screenshot/no_screenshot.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/form_verification_controller.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/form_identification_controller.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/form_self_identification_controller.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/form_terms_controller.dart';
import 'package:pdl_superapp/controllers/network_controller.dart';
import 'package:pdl_superapp/services/background_upload_service.dart';
import 'package:pdl_superapp/services/pending_photo_service.dart';
import 'package:pdl_superapp/models/recruitment_form_model.dart';
import 'package:pdl_superapp/models/recruitment_api_model.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/form_firestore_service.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';

class RecruitmentFormController extends BaseControllers {
  PageController pageController = PageController();
  RxInt activePage = 0.obs;

  RxBool isVerificationEmailSent = false.obs;

  late SharedPreferences prefs;

  // Form ID untuk Firestore
  RxString formId = ''.obs;
  bool hasServerUuid = false;

  // API Model untuk prefill (jika diakses dari approval)
  RecruitmentApiModel? apiModel;

  // Status form
  RxBool isFormLoaded = false.obs;
  RxBool isFormSaving = false.obs;
  RxBool isFormSubmitted = false.obs;
  RxString formStatus = 'draft'.obs;
  RxBool isFormDisabled = false.obs;

  String currentAgentCode = '';

  // Timer untuk auto-save
  Timer? _autoSaveTimer;
  final int _autoSaveIntervalSeconds = 5; // Auto-save setiap 5 detik

  // Flag untuk mencegah auto-save saat form sedang di-load
  bool _isFormInitializing = true;

  // Flag untuk mencegah pembuatan form Firestore sampai save button diklik pertama kali
  bool _hasBeenSavedOnce = false;

  // Service untuk Firestore
  final FormFirestoreService _firestoreService = FormFirestoreService();

  // Instance untuk mencegah screenshot
  final NoScreenshot _noScreenshot = NoScreenshot.instance;

  // Sub-controllers
  late FormVerificationController verificationController;
  late FormIdentificationController identificationController;
  late FormSelfIdentificationController selfIdentificationController;
  late FormTermsController termsController;

  RxBool isReady = false.obs;

  RxBool formLoading = false.obs;

  late NetworkController networkController;

  @override
  void onInit() async {
    super.onInit();
    if (!kIsWeb) {
      try {
        networkController = Get.find<NetworkController>();
      } catch (e) {
        // Fallback jika belum diinisialisasi
        networkController = Get.put(NetworkController());
      }
    }
    prefs = await SharedPreferences.getInstance();

    currentAgentCode = prefs.getString(kStorageAgentCode) ?? '';

    // Disable screenshot untuk keamanan form recruitment
    // await _disableScreenshot();

    // Initialize sub-controllers
    verificationController = Get.put(
      FormVerificationController(baseController: this),
    );
    identificationController = Get.put(
      FormIdentificationController(baseController: this),
    );
    selfIdentificationController = Get.put(
      FormSelfIdentificationController(baseController: this),
    );
    termsController = Get.put(FormTermsController(baseController: this));

    // Inisialisasi form ID atau ambil dari parameter jika ada
    _initFormId();
    // Setup listener untuk perubahan form
    _setupFormChangeListeners();
    // Setup trim listeners untuk semua text fields
    _setupTrimListeners();

    pageController.addListener(() {
      activePage.value = pageController.page!.round();
    });
  }

  @override
  void onReady() {
    super.onReady();
    isReady.value = true;
  }

  // Delegate to sub-controllers for API responses
  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    // Handle submit recruitment form response
    if (requestCode == kReqSubmitRecruitmentForm) {
      _handleSubmitFormSuccess(response);
      return;
    }

    // Delegate to appropriate sub-controller
    verificationController.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    selfIdentificationController.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    termsController.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    setLoading(false);
    try {
      String errorMessage = 'Terjadi Kesalahan harap ulangi kembali';

      // Check if response has validation errors
      if (response.body['validation'] != null &&
          response.body['validation'] is Map) {
        final validation = response.body['validation'] as Map;
        final List<String> validationMessages = [];

        // Extract validation messages
        validation.forEach((field, error) {
          if (error is Map && error['message'] != null) {
            validationMessages.add(error['message'].toString());
          }
        });

        if (validationMessages.isNotEmpty) {
          errorMessage = validationMessages.join('\n');
        }
      } else if (response.body['message'] != null) {
        errorMessage = response.body['message'].toString();
      } else if (response.body['error_description'] != null) {
        errorMessage = response.body['error_description'].toString();
      }

      Get.snackbar(
        'Gagal',
        errorMessage,
        colorText: Colors.white,
        backgroundColor: Colors.red,
        snackPosition: SnackPosition.BOTTOM,
        duration: const Duration(
          seconds: 5,
        ), // Longer duration for detailed messages
      );
    } catch (e) {
      // Get.snackbar(
      //   'Gagal',
      //   'Terjadi Kesalahan harap ulangi kembali',
      //   colorText: Colors.white,
      //   backgroundColor: Colors.red,
      //   snackPosition: SnackPosition.BOTTOM,
      // );
    }
  }

  // Handle submit form success response
  void _handleSubmitFormSuccess(response) async {
    log('Submit form success response: $response');

    // Update form status to submitted
    isFormSubmitted.value = true;
    formStatus.value = 'submitted';

    // Delete form from Firestore (both local and online) since it's successfully submitted
    try {
      log(
        'Deleting form from Firestore after successful submission: ${formId.value}',
      );
      final deleteResult = await _firestoreService.deleteRecruitmentForm(
        formId.value,
      );
      if (deleteResult) {
        log('Successfully deleted form from Firestore: ${formId.value}');
      } else {
        log('Failed to delete form from Firestore: ${formId.value}');
      }
    } catch (e) {
      log('Error deleting form from Firestore: $e');
    }

    // Show success message
    Get.snackbar(
      'Berhasil',
      'Form berhasil disubmit.',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: kColorGlobalBgGreen,
      colorText: kColorGlobalGreen,
      margin: EdgeInsets.only(bottom: paddingMedium),
    );

    // Navigate back to recruitment list
    Get.offNamedUntil(Routes.MENU_KEAGENAN, (route) => false);
  }

  // Load recruiter data from SharedPreferences
  void loadRecruiterDataFromPrefs() {
    verificationController.loadRecruiterDataFromPrefs();
  }

  // Inisialisasi form ID
  Future<void> _initFormId() async {
    // Cek apakah ada API model dari arguments (dari halaman approval)
    if (Get.arguments != null &&
        Get.arguments['apiData'] is RecruitmentApiModel) {
      apiModel = Get.arguments['apiData'] as RecruitmentApiModel;
      formId.value = apiModel!.uuid ?? const Uuid().v4();
      log('Menggunakan API model dari approval: ${formId.value}');
      // Populate form dengan data dari API model
      _populateFormWithApiData(apiModel!);
    }
    // Cek apakah ada form ID dari parameter (dari Firestore)
    else if (Get.parameters.containsKey('formId')) {
      formId.value = Get.parameters['formId']!;
      log('Menggunakan form ID dari parameter: ${formId.value}');
      // Load form data dari Firestore
      _loadFormData();
    } else {
      // Buat form ID baru menggunakan UUID
      formId.value = const Uuid().v4();
      log('Membuat form ID baru: ${formId.value}');
      loadRecruiterDataFromPrefs();

      // Enable auto-save untuk form baru setelah data recruiter di-load
      Future.delayed(Duration(milliseconds: 100), () {
        _isFormInitializing = false;
      });
    }
  }

  // Setup listener untuk perubahan form
  void _setupFormChangeListeners() {
    // Setup listeners for sub-controllers to notify main controller
    // This will be handled by each sub-controller calling onFormChanged()
  }

  // Setup trim listeners untuk semua text fields
  void _setupTrimListeners() {
    // Delegate to sub-controllers to setup their trim listeners
    verificationController.setupTrimListeners();
    identificationController.setupTrimListeners();
    selfIdentificationController.setupTrimListeners();
    // termsController tidak memiliki text fields yang perlu di-trim
  }

  // Trim semua field sebelum submit
  void _trimAllFields() {
    // Trim verification fields
    verificationController.candidateLevelController.text =
        verificationController.candidateLevelController.text.trim();
    verificationController.candidateBranchController.text =
        verificationController.candidateBranchController.text.trim();

    // Trim identification fields
    identificationController.nikController.text =
        identificationController.nikController.text.trim();
    identificationController.namaKtpController.text =
        identificationController.namaKtpController.text.trim();
    identificationController.tempatLahirController.text =
        identificationController.tempatLahirController.text.trim();
    identificationController.tanggalLahirController.text =
        identificationController.tanggalLahirController.text.trim();
    identificationController.bulanLahirController.text =
        identificationController.bulanLahirController.text.trim();
    identificationController.tahunLahirController.text =
        identificationController.tahunLahirController.text.trim();
    identificationController.alamatKtpController.text =
        identificationController.alamatKtpController.text.trim();
    identificationController.rtKtpController.text =
        identificationController.rtKtpController.text.trim();
    identificationController.rwKtpController.text =
        identificationController.rwKtpController.text.trim();
    identificationController.provinsiKtpController.text =
        identificationController.provinsiKtpController.text.trim();
    identificationController.kabupatenKtpController.text =
        identificationController.kabupatenKtpController.text.trim();
    identificationController.kecamatanKtpController.text =
        identificationController.kecamatanKtpController.text.trim();
    identificationController.kelurahanKtpController.text =
        identificationController.kelurahanKtpController.text.trim();

    // Trim domisili fields
    identificationController.alamatDomisiliController.text =
        identificationController.alamatDomisiliController.text.trim();
    identificationController.rtDomisiliController.text =
        identificationController.rtDomisiliController.text.trim();
    identificationController.rwDomisiliController.text =
        identificationController.rwDomisiliController.text.trim();
    identificationController.provinsiDomisiliController.text =
        identificationController.provinsiDomisiliController.text.trim();
    identificationController.kabupatenDomisiliController.text =
        identificationController.kabupatenDomisiliController.text.trim();
    identificationController.kecamatanDomisiliController.text =
        identificationController.kecamatanDomisiliController.text.trim();
    identificationController.kelurahanDomisiliController.text =
        identificationController.kelurahanDomisiliController.text.trim();

    // Trim self identification fields
    selfIdentificationController.emailController.text =
        selfIdentificationController.emailController.text.trim();
    selfIdentificationController.nomorHpController.text =
        selfIdentificationController.nomorHpController.text.trim();
    selfIdentificationController.pekerjaanController.text =
        selfIdentificationController.pekerjaanController.text.trim();
    selfIdentificationController.pekerjaanCodeController.text =
        selfIdentificationController.pekerjaanCodeController.text.trim();
    selfIdentificationController.emergencyNamaController.text =
        selfIdentificationController.emergencyNamaController.text.trim();
    selfIdentificationController.emergencyHubunganController.text =
        selfIdentificationController.emergencyHubunganController.text.trim();
    selfIdentificationController.emergencyNomorHpController.text =
        selfIdentificationController.emergencyNomorHpController.text.trim();
    selfIdentificationController.namaPemilikRekeningController.text =
        selfIdentificationController.namaPemilikRekeningController.text.trim();
    selfIdentificationController.nomorRekeningController.text =
        selfIdentificationController.nomorRekeningController.text.trim();
    selfIdentificationController.namaBankController.text =
        selfIdentificationController.namaBankController.text.trim();
  }

  // Handler ketika form berubah - called by sub-controllers
  void onFormChanged() {
    // Jangan auto-save jika form sedang dalam proses inisialisasi
    if (_isFormInitializing) {
      return;
    }

    // Jangan auto-save jika form berasal dari API (sudah submitted)
    if (apiModel != null) {
      return;
    }

    // Jangan auto-save jika belum pernah disave secara manual
    if (!_hasBeenSavedOnce) {
      return;
    }

    // Jangan auto-save jika sedang dalam proses saving
    if (isFormSaving.value) {
      return;
    }

    // Jika timer sudah berjalan, reset
    _autoSaveTimer?.cancel();

    // Mulai timer baru untuk auto-save
    _autoSaveTimer = Timer(Duration(seconds: _autoSaveIntervalSeconds), () {
      // Simpan form data dengan flag auto-save
      _performAutoSave();
    });
  }

  // Perform auto-save dengan handling khusus
  Future<void> _performAutoSave() async {
    try {
      // Skip jika sedang saving atau form sudah submitted
      if (isFormSaving.value || isFormSubmitted.value) {
        return;
      }

      log('Performing auto-save for form: ${formId.value}');

      // Buat model dari data form saat ini
      final formData = await _createFormModel(isSubmit: false);

      // Simpan dengan flag auto-save
      final result = await _firestoreService.saveRecruitmentForm(
        formData,
        formId.value,
        isAutoSave: true,
      );

      if (result) {
        log('Auto-save berhasil untuk form: ${formId.value}');
      } else {
        log('Auto-save gagal untuk form: ${formId.value}');
      }
    } catch (e) {
      log('Error during auto-save: $e');
    }
  }

  // Load form data dari Firestore
  Future<void> _loadFormData() async {
    if (formId.value.isEmpty) {
      log('Form ID kosong, tidak dapat memuat data form');
      return;
    }

    setLoading(true);

    try {
      final formData = await _firestoreService.getRecruitmentForm(formId.value);

      if (formData != null) {
        // Isi form dengan data yang ada
        _populateFormWithData(formData);
        isFormLoaded.value = true;
        log('Berhasil memuat data form dengan ID: ${formId.value}');
      } else {
        log('Tidak ada data form dengan ID: ${formId.value}');
      }
    } catch (e) {
      log('Error saat memuat data form: $e');
    } finally {
      setLoading(false);
    }
  }

  // Delegate form population to sub-controllers (from Firestore)
  void _populateFormWithData(RecruitmentFormModel formData) {
    // Delegate to sub-controllers
    verificationController.populateFormData(formData);
    identificationController.populateFormData(formData);
    selfIdentificationController.populateFormData(formData);
    termsController.populateFormData(formData);

    // Set status form
    isFormSubmitted.value = formData.isSubmitted ?? false;
    formStatus.value = formData.formStatus ?? 'draft';

    hasServerUuid = formData.hasServerUuid ?? false;
    loadRecruiterDataFromPrefs();

    // Mark form as loaded
    isFormLoaded.value = true;

    log('Berhasil memuat data form dari Firestore: ${formData.id}');

    // Set flag bahwa form sudah pernah disave karena data sudah ada di Firestore
    _hasBeenSavedOnce = true;

    // Enable auto-save setelah form selesai di-load
    _isFormInitializing = false;

    // Handle navigation to specific page if provided in arguments
    if (Get.arguments != null && Get.arguments['page'] != null) {
      Future.delayed(Duration(milliseconds: 100)).then((val) {
        activePage.value = Get.arguments['page'] ?? 0;
        goToPage(activePage.value);
      });
    }
  }

  // Populate form with data from API model (from approval page)
  void _populateFormWithApiData(RecruitmentApiModel apiData) {
    // Delegate to sub-controllers
    verificationController.populateFormDataFromApi(apiData);
    identificationController.populateFormDataFromApi(apiData);
    selfIdentificationController.populateFormDataFromApi(apiData);
    termsController.populateFormDataFromApi(apiData);

    // Set status form - API data is already submitted
    isFormSubmitted.value = true;
    formStatus.value = 'submitted';
    hasServerUuid = true;
    //tertunda di approval status == dikembalikan
    if (apiData.approvalStatus == 'TERTUNDA') {
      if ((apiData.recruiter?.agentCode ?? apiData.recruiter?.username) ==
          currentAgentCode) {
        isFormDisabled.value = false;
      } else {
        isFormDisabled.value = true;
      }
    } else {
      if (apiData.approvalStatus == null) {
        isFormDisabled.value = false;
      } else {
        isFormDisabled.value = true;
      }
    }

    // Mark form as loaded
    isFormLoaded.value = true;

    log('Berhasil memuat data form dari API model: ${apiData.uuid}');

    // Enable auto-save setelah form selesai di-load
    _isFormInitializing = false;

    if (Get.arguments != null) {
      Future.delayed(Duration(milliseconds: 100)).then((val) {
        activePage.value = Get.arguments['page'] ?? 0;
        goToPage(activePage.value);
      });
    }
  }

  // Simpan form data ke Firestore
  Future<bool> saveFormData({bool isSubmit = false}) async {
    if (formId.value.isEmpty) {
      log('Form ID kosong, tidak dapat menyimpan data form');
      return false;
    }

    isFormSaving.value = true;
    try {
      // Jika ini adalah submit final, upload foto yang belum terupload
      // check connection, if available upload foto
      // Note: Background upload service will handle automatic uploads,
      // but we still do a final check here for immediate upload

      if (!kIsWeb) {
        final isOnline = networkController.isConnected.value;
        if (isOnline) {
          log('Final submit - checking and uploading pending photos...');
          final photosUploaded =
              await verificationController.uploadPendingPhotos();
          if (!photosUploaded) {
            log('Failed to upload pending photos');
            Utils.popup(
              body: 'Failed to upload pending photos',
              type: kPopupFailed,
            );
          } else {
            Utils.popup(body: 'All Photo Uploaded', type: kPopupSuccess);
          }
          log('All photos uploaded successfully');
        }
      }
      // Buat model dari data form saat ini (dengan URL foto yang sudah terupdate)
      final formData = await _createFormModel(isSubmit: isSubmit);

      // Simpan ke Firestore (manual save, bukan auto-save)
      final result = await _firestoreService.saveRecruitmentForm(
        formData,
        formId.value,
        isAutoSave: false,
      );

      if (result) {
        log('Berhasil menyimpan data form dengan ID: ${formId.value}');

        // Set flag bahwa form sudah pernah disave, sehingga auto-save bisa berjalan
        _hasBeenSavedOnce = true;

        if (isSubmit) {
          isFormSubmitted.value = true;
          formStatus.value = 'submitted';
        }
      } else {
        log('Gagal menyimpan data form dengan ID: ${formId.value}');
      }

      return result;
    } catch (e) {
      log('Error saat menyimpan data form: $e');
      return false;
    } finally {
      isFormSaving.value = false;
    }
  }

  // Buat model dari data form saat ini - delegate to sub-controllers
  Future<RecruitmentFormModel> _createFormModel({bool isSubmit = false}) async {
    return RecruitmentFormModel(
      id: formId.value,

      // Get data from sub-controllers
      recruiterName: verificationController.recruiterName.value,
      recruiterId: verificationController.recruiterId.value,
      recruiterBranch: verificationController.recruiterBranch.value,
      recruiterCode: verificationController.recruiterCode.value,
      recruiterLevel: verificationController.recruiterLevel.value,
      recruiterPhoto: verificationController.recruiterPhoto.value,
      candidateLevel: verificationController.candidateLevelController.text,
      candidateBranch: verificationController.candidateBranchController.text,
      candidateBranchCode: verificationController.candidateBranchCode.value,

      // Data FormIdentification
      nik: identificationController.nikController.text,
      namaKtp: identificationController.namaKtpController.text,
      tempatLahir: identificationController.tempatLahirController.text,
      tanggalLahir: identificationController.tanggalLahirController.text,
      bulanLahir: identificationController.bulanLahirController.text,
      tahunLahir: identificationController.tahunLahirController.text,
      jenisKelamin: identificationController.jenisKelaminController.text,
      alamatKtp: identificationController.alamatKtpController.text,
      rtKtp: identificationController.rtKtpController.text,
      rwKtp: identificationController.rwKtpController.text,
      provinsiKtp: identificationController.provinsiKtpController.text,
      kabupatenKtp: identificationController.kabupatenKtpController.text,
      kecamatanKtp: identificationController.kecamatanKtpController.text,
      kelurahanKtp: identificationController.kelurahanKtpController.text,
      maritalStatus: identificationController.selectedMaritalStatus.value
          .replaceAll(' ', '_'),

      // Data Alamat Domisili
      alamatDomisili: identificationController.alamatDomisiliController.text,
      rtDomisili: identificationController.rtDomisiliController.text,
      rwDomisili: identificationController.rwDomisiliController.text,
      provinsiDomisili:
          identificationController.provinsiDomisiliController.text,
      kabupatenDomisili:
          identificationController.kabupatenDomisiliController.text,
      kecamatanDomisili:
          identificationController.kecamatanDomisiliController.text,
      kelurahanDomisili:
          identificationController.kelurahanDomisiliController.text,
      isDomicileSameAsKtp:
          identificationController.selectedIsAddressSame.value == 0,

      // Data FormSelfIdentification
      email: selfIdentificationController.emailController.text,
      nomorHp: selfIdentificationController.nomorHpController.text,
      occupation: selfIdentificationController.pekerjaanController.text,
      occupationCode: selfIdentificationController.pekerjaanCodeController.text,
      emergencyNama: selfIdentificationController.emergencyNamaController.text,
      emergencyHubungan:
          selfIdentificationController.emergencyHubunganController.text,
      emergencyNomorHp:
          selfIdentificationController.emergencyNomorHpController.text,
      namaPemilikRekening:
          selfIdentificationController.namaPemilikRekeningController.text,
      nomorRekening: selfIdentificationController.nomorRekeningController.text,
      namaBank: selfIdentificationController.namaBankController.text,
      bankCode: selfIdentificationController.bankCode.value,

      // Data Foto - mobile only (web tidak perlu offline storage)
      ktpImagePath:
          !kIsWeb ? _getImagePath(verificationController.ktpImage.value) : null,
      selfieKtpImagePath:
          !kIsWeb
              ? _getImagePath(verificationController.selfieKtpImage.value)
              : null,
      pasFotoImagePath:
          !kIsWeb
              ? _getImagePath(verificationController.pasFotoImage.value)
              : null,

      // Data URL Foto
      ktpImageUrl: verificationController.ktpUrl.value,
      selfieKtpImageUrl: verificationController.selfieKtpUrl.value,
      pasFotoImageUrl: verificationController.pasFotoUrl.value,

      // Terms and Signature
      signature: termsController.signatureData.value,
      paraf: termsController.parafData.value,

      // Signature and Paraf URLs
      signatureUrl: termsController.signatureUrl.value,
      parafUrl: termsController.parafUrl.value,

      // Last job field
      lastJob: selfIdentificationController.lastJob.value,

      // New structured data fields
      last5YearJobData:
          selfIdentificationController.last5YearJobData
              .map((item) => item.toJson())
              .toList(),
      last2YearProductionData:
          selfIdentificationController.last2YearProductionData
              .map((item) => item.toJson())
              .toList(),
      lastCompanyManPowerData:
          selfIdentificationController.lastCompanyManPowerData.value.toJson(),
      rewardInfoData:
          selfIdentificationController.rewardInfoData
              .map((item) => item.toJson())
              .toList(),

      // Metadata
      lastUpdated: DateTime.now().millisecondsSinceEpoch,
      isSubmitted: isSubmit ? true : isFormSubmitted.value,
      formStatus: isSubmit ? 'submitted' : formStatus.value,
      hasServerUuid:
          false, // Default false, akan diupdate setelah mendapat UUID dari server
    );
  }

  // Helper method to get image path from either File or XFile
  String? _getImagePath(dynamic imageFile) {
    if (imageFile == null) return null;

    try {
      if (kIsWeb) {
        // On web, we can't store local paths, so we'll store as base64 or skip
        // For now, return null to avoid storing invalid paths
        return null;
      } else {
        // On mobile, get the path from File
        return imageFile.path;
      }
    } catch (e) {
      log('Error getting image path: $e');
      return null;
    }
  }

  Future<bool> submitForm() async {
    return await saveFormData(isSubmit: true);
  }

  // Update form ID setelah mendapat UUID dari server
  Future<bool> updateFormId(String newFormId) async {
    try {
      if (formId.value.isEmpty || newFormId.isEmpty) {
        log('Cannot update form ID: old or new ID is empty');
        return false;
      }

      if (formId.value == newFormId) {
        log('Form ID is already the same, no update needed');
        return true;
      }

      String oldFormId = formId.value;
      log('Updating form ID from $oldFormId to $newFormId');

      // Pindahkan data form dari ID lama ke ID baru di Firestore
      final moveResult = await _firestoreService.moveRecruitmentForm(
        oldFormId,
        newFormId,
      );

      if (moveResult) {
        // Update form ID di controller
        formId.value = newFormId;
        log('Successfully updated form ID to: $newFormId');
        return true;
      } else {
        log('Failed to move form data from $oldFormId to $newFormId');
        return false;
      }
    } catch (e) {
      log('Error updating form ID: $e');
      return false;
    }
  }

  // Submit form for final submission
  Future<bool> submitFormFinal() async {
    setLoading(true);
    // Trim semua field sebelum submit
    _trimAllFields();

    Future.delayed(Duration(seconds: 1)).then((val) {
      var data = {
        "recruiterCode": verificationController.recruiterCode.value,
        "ktpPhoto": verificationController.ktpUrl.value,
        "selfiePhoto": verificationController.selfieKtpUrl.value,
        "passPhoto": verificationController.pasFotoUrl.value,
        "positionLevel":
            verificationController.candidateLevelController.text.trim(),
        "branch": verificationController.candidateBranchCode.value,
        "bank": selfIdentificationController.bankCode.value,
        "nik": identificationController.nikController.text.trim(),
        "fullName": identificationController.namaKtpController.text.trim(),
        "birthPlace":
            identificationController.tempatLahirController.text.trim(),
        "birthDate":
            "${identificationController.tahunLahirController.text}-${((identificationController.monthList.indexOf(identificationController.bulanLahirController.text) + 1)).toString().padLeft(2, '0')}-${identificationController.tanggalLahirController.text.padLeft(2, '0')}",
        "gender":
            identificationController.selectedJenisKelamin.value == 0
                ? 'M'
                : 'F',
        "ktpProvince": identificationController.provinsiKtpController.text,
        "ktpCity": identificationController.kabupatenKtpController.text,
        "ktpDistrict": identificationController.kecamatanKtpController.text,
        "ktpSubDistrict": identificationController.kelurahanKtpController.text,
        "ktpRt": identificationController.rtKtpController.text,
        "ktpRw": identificationController.rwKtpController.text,
        "ktpAddress": identificationController.alamatKtpController.text,
        "isDomicileSameAsKtp":
            identificationController.selectedIsAddressSame.value == 0
                ? true
                : false,
        "domicileProvince":
            identificationController.selectedIsAddressSame.value == 0
                ? identificationController.provinsiKtpController.text
                : identificationController.provinsiDomisiliController.text,
        "domicileCity":
            identificationController.selectedIsAddressSame.value == 0
                ? identificationController.kabupatenKtpController.text
                : identificationController.kabupatenDomisiliController.text,
        "domicileDistrict":
            identificationController.selectedIsAddressSame.value == 0
                ? identificationController.kecamatanKtpController.text
                : identificationController.kecamatanDomisiliController.text,
        "domicileSubDistrict":
            identificationController.selectedIsAddressSame.value == 0
                ? identificationController.kelurahanKtpController.text
                : identificationController.kelurahanDomisiliController.text,
        "domicileRt":
            identificationController.selectedIsAddressSame.value == 0
                ? identificationController.rtKtpController.text
                : identificationController.rtDomisiliController.text,
        "domicileRw":
            identificationController.selectedIsAddressSame.value == 0
                ? identificationController.rwKtpController.text
                : identificationController.rwDomisiliController.text,
        "domicileAddress":
            identificationController.selectedIsAddressSame.value == 0
                ? identificationController.alamatKtpController.text
                : identificationController.alamatDomisiliController.text,
        "phoneNumber":
            selfIdentificationController.nomorHpController.text.startsWith('08')
                ? selfIdentificationController.nomorHpController.text
                : '08${selfIdentificationController.nomorHpController.text}',
        "maritalStatus": getMaritalStatus(
          identificationController.selectedMaritalStatus.value.replaceAll(
            ' ',
            '_',
          ),
        ),
        "occupation": selfIdentificationController.pekerjaanController.text,
        "occupationCode":
            selfIdentificationController.pekerjaanCodeController.text,
        "email": selfIdentificationController.emailController.text,
        "emergencyContactName":
            selfIdentificationController.emergencyNamaController.text,
        "emergencyContactRelation":
            selfIdentificationController.emergencyHubunganController.text,
        "emergencyContactPhone":
            selfIdentificationController.emergencyNomorHpController.text
                    .startsWith('08')
                ? selfIdentificationController.emergencyNomorHpController.text
                : '08${selfIdentificationController.emergencyNomorHpController.text}',
        "bankAccountName":
            selfIdentificationController.namaPemilikRekeningController.text,
        "bankAccountNumber":
            selfIdentificationController.nomorRekeningController.text,
        "lastJob": selfIdentificationController.lastJob.value,
        "signature": termsController.signatureUrl.value,
        "paraf": termsController.parafUrl.value,
        "last5YearJobData": [
          for (
            int i = 0;
            i < selfIdentificationController.last5YearJobData.length;
            i++
          )
            {
              "year": selfIdentificationController.last5YearJobData[i].year,
              "startYear":
                  selfIdentificationController.last5YearJobData[i].startYear,
              "endYear":
                  selfIdentificationController.last5YearJobData[i].endYear,
              "company":
                  selfIdentificationController.last5YearJobData[i].company,
              "position":
                  selfIdentificationController.last5YearJobData[i].position,
            },
        ],
        // selfIdentificationController.last5YearJobData
        //     .map((item) => item.toJson())
        //     .toList(),
        "last2YearProductionData":
            selfIdentificationController.last2YearProductionData
                .map((item) => item.toJson())
                .toList(),
        "lastCompanyManPowerData":
            selfIdentificationController.lastCompanyManPowerData.value.toJson(),
        "rewardInfoData":
            selfIdentificationController.rewardInfoData
                .map((item) => item.toJson())
                .toList(),
      };
      log('Final submit data: $data');

      if (apiModel?.trxStatus == 'DIKEMBALIKAN') {
        // api buat revise disini
        setLoading(true);
        api.performPatchRecruitmentForm(
          controllers: this,
          uuid: apiModel!.uuid!,
          data: data,
          code: kReqSubmitRecruitmentForm,
        );
        return true;
      }

      // Submit form to API
      setLoading(true);
      api.performSubmitRecruitmentForm(
        controllers: this,
        data: data,
        code: kReqSubmitRecruitmentForm,
      );
    });
    return true;
  }

  String getMaritalStatus(String status) {
    String res = status.replaceAll(' ', '_');
    if (res == 'BELUMKAWIN') {
      res = 'BELUM_KAWIN';
    }
    if (res == 'CERAIMATI') {
      res = 'CERAI_MATI';
    }
    return res;
  }

  // Submit form
  Future<bool> submitFormForVerification() async {
    // Trim semua field sebelum submit
    _trimAllFields();

    if (apiModel?.trxStatus == 'DIKEMBALIKAN') {
      pageController.nextPage(
        duration: Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      return true;
    }

    final isOnline = networkController.isConnected.value;
    if (isOnline) {
      log('Final submit - checking and uploading pending photos...');
      final photosUploaded = await verificationController.uploadPendingPhotos();
      if (!photosUploaded) {
        log('Failed to upload pending photos');
        Utils.popup(
          body: 'Failed to upload pending photos',
          type: kPopupFailed,
        );
      } else {
        Utils.popup(body: 'All Photo Uploaded', type: kPopupSuccess);
      }
      log('All photos uploaded successfully');
    }

    var data = {
      "recruiterCode": verificationController.recruiterCode.value,
      "ktpPhoto": verificationController.ktpUrl.value,
      "selfiePhoto": verificationController.selfieKtpUrl.value,
      "passPhoto": verificationController.pasFotoUrl.value,
      "positionLevel":
          verificationController.candidateLevelController.text.trim(),
      "branch": verificationController.candidateBranchCode.value,
      "bank": selfIdentificationController.bankCode.value,
      "nik": identificationController.nikController.text.trim(),
      "fullName": identificationController.namaKtpController.text.trim(),
      "birthPlace": identificationController.tempatLahirController.text.trim(),
      "birthDate":
          "${identificationController.tahunLahirController.text}-${((identificationController.monthList.indexOf(identificationController.bulanLahirController.text) + 1)).toString().padLeft(2, '0')}-${identificationController.tanggalLahirController.text.padLeft(2, '0')}",
      "gender":
          identificationController.selectedJenisKelamin.value == 0 ? 'M' : 'F',
      "ktpProvince": identificationController.provinsiKtpController.text.trim(),
      "ktpCity": identificationController.kabupatenKtpController.text.trim(),
      "ktpDistrict":
          identificationController.kecamatanKtpController.text.trim(),
      "ktpSubDistrict":
          identificationController.kelurahanKtpController.text.trim(),
      "ktpRt": identificationController.rtKtpController.text.trim(),
      "ktpRw": identificationController.rwKtpController.text.trim(),
      "ktpAddress": identificationController.alamatKtpController.text.trim(),
      "isDomicileSameAsKtp":
          identificationController.selectedIsAddressSame.value == 0
              ? true
              : false,
      "domicileProvince":
          identificationController.selectedIsAddressSame.value == 0
              ? identificationController.provinsiKtpController.text.trim()
              : identificationController.provinsiDomisiliController.text.trim(),
      "domicileCity":
          identificationController.selectedIsAddressSame.value == 0
              ? identificationController.kabupatenKtpController.text.trim()
              : identificationController.kabupatenDomisiliController.text
                  .trim(),
      "domicileDistrict":
          identificationController.selectedIsAddressSame.value == 0
              ? identificationController.kecamatanKtpController.text.trim()
              : identificationController.kecamatanDomisiliController.text
                  .trim(),
      "domicileSubDistrict":
          identificationController.selectedIsAddressSame.value == 0
              ? identificationController.kelurahanKtpController.text.trim()
              : identificationController.kelurahanDomisiliController.text
                  .trim(),
      "domicileRt":
          identificationController.selectedIsAddressSame.value == 0
              ? identificationController.rtKtpController.text.trim()
              : identificationController.rtDomisiliController.text.trim(),
      "domicileRw":
          identificationController.selectedIsAddressSame.value == 0
              ? identificationController.rwKtpController.text.trim()
              : identificationController.rwDomisiliController.text.trim(),
      "domicileAddress":
          identificationController.selectedIsAddressSame.value == 0
              ? identificationController.alamatKtpController.text.trim()
              : identificationController.alamatDomisiliController.text.trim(),
      "phoneNumber": selfIdentificationController.nomorHpController.text,
      "maritalStatus": getMaritalStatus(
        identificationController.selectedMaritalStatus.value.replaceAll(
          ' ',
          '_',
        ),
      ),
      "occupation":
          selfIdentificationController.pekerjaanController.text.trim(),
      "occupationCode":
          selfIdentificationController.pekerjaanCodeController.text.trim(),
      "email": selfIdentificationController.emailController.text.trim(),
      "emergencyContactName":
          selfIdentificationController.emergencyNamaController.text.trim(),
      "emergencyContactRelation":
          selfIdentificationController.emergencyHubunganController.text.trim(),
      "emergencyContactPhone":
          selfIdentificationController.emergencyNomorHpController.text.trim(),
      "bankAccountName":
          selfIdentificationController.namaPemilikRekeningController.text
              .trim(),
      "bankAccountNumber":
          selfIdentificationController.nomorRekeningController.text.trim(),
      "lastJob": selfIdentificationController.lastJob.value,
      "signature": termsController.signatureUrl.value,
      "paraf": termsController.parafUrl.value,
      "last5YearJobData": [
        for (
          int i = 0;
          i < selfIdentificationController.last5YearJobData.length;
          i++
        )
          {
            "year": selfIdentificationController.last5YearJobData[i].year,
            "startYear":
                selfIdentificationController.last5YearJobData[i].startYear,
            "endYear": selfIdentificationController.last5YearJobData[i].endYear,
            "company": selfIdentificationController.last5YearJobData[i].company,
            "position":
                selfIdentificationController.last5YearJobData[i].position,
          },
      ],
      // selfIdentificationController.last5YearJobData
      //     .map((item) => item.toJson())
      //     .toList(),
      "last2YearProductionData":
          selfIdentificationController.last2YearProductionData
              .map((item) => item.toJson())
              .toList(),
      "lastCompanyManPowerData":
          selfIdentificationController.lastCompanyManPowerData.value.toJson(),
      "rewardInfoData":
          selfIdentificationController.rewardInfoData
              .map((item) => item.toJson())
              .toList(),
    };

    log('data: $data');
    // 1. send data as draft
    // kalau data sudah di server (hasServerUuid == true) tidak perlu di send as draft lagi

    if (hasServerUuid) {
      // send Verification
      setLoading(true);
      selfIdentificationController.sendVerification(formId.value);
    } else {
      setLoading(true);
      api.performSaveDraft(controllers: this, data: data, code: kReqSaveDraft);
    }
    return true;
  }

  // Delegate image picking to verification controller
  Future<void> pickKtpImage(String title, String type) async {
    await verificationController.pickKtpImage(title, type);
  }

  // Delegate image clearing to verification controller
  void clearImage(String type) {
    verificationController.clearImage(type);
  }

  // Delegate KTP OCR processing to identification controller
  Future<void> processKtpOcr(file) async {
    await identificationController.processKtpOcr(file);
  }

  // Delegate branch text change to verification controller
  void onBranchTextChanged(String value) {
    verificationController.onBranchTextChanged(value);
  }

  // Validate current page before navigation
  bool validateCurrentPage() {
    switch (activePage.value) {
      case 0:
        return verificationController.validateForm();
      case 1:
        return identificationController.validateForm();
      case 2:
        return selfIdentificationController.validateForm();
      case 3:
        return termsController.validateForm();
      default:
        return true;
    }
  }

  // Navigation methods
  void nextPage() {
    if (activePage.value < 3) {
      activePage.value++;
      pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void previousPage() {
    if (activePage.value > 0) {
      activePage.value--;
      pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void goToPage(int page) {
    if (page >= 0 && page <= 3) {
      activePage.value = page;
      pageController.animateToPage(
        page,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  // Fungsi untuk disable screenshot
  // Future<void> _disableScreenshot() async {
  //   try {
  //     bool result = await _noScreenshot.screenshotOff();
  //     log('Screenshot disabled: $result');
  //   } catch (e) {
  //     log('Error disabling screenshot: $e');
  //   }
  // }

  // Fungsi untuk enable screenshot
  Future<void> _enableScreenshot() async {
    try {
      bool result = await _noScreenshot.screenshotOn();
      log('Screenshot enabled: $result');
    } catch (e) {
      log('Error enabling screenshot: $e');
    }
  }

  @override
  void onClose() {
    // Enable kembali screenshot saat keluar dari halaman
    _enableScreenshot();

    // Batalkan timer auto-save
    _autoSaveTimer?.cancel();

    // Dispose page controller
    pageController.dispose();

    // Dispose sub-controllers
    Get.delete<FormVerificationController>();
    Get.delete<FormIdentificationController>();
    Get.delete<FormSelfIdentificationController>();
    Get.delete<FormTermsController>();

    super.onClose();
  }
}
