import 'package:fbroadcast/fbroadcast.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/simple_profile_models.dart';
import 'package:pdl_superapp/models/widget/widget_production_models.dart';
import 'package:pdl_superapp/utils/keys.dart';

/// Controller for the Production Detail Widget page
///
/// Handles data fetching, filtering, and state management for production data
/// for a specific agent (Direct BP).
class ProductionIndividuDetailWidgetController extends BaseControllers {
  late SharedPreferences prefs;

  // Period selection state
  final RxString selectedMonth = kSwitchMonthly.obs;
  final RxString selectedType = kSwitchProdIndividu.obs;

  RxDouble totalNetApe = 0.0.obs;

  // Agent code for which to fetch data
  final RxString agentCode = ''.obs;

  // User level for determining data source
  final RxString userLevel = ''.obs;
  final RxString userChannel = ''.obs;

  // Agent profile data
  final Rx<SimpleProfileModels?> agentProfile = Rx<SimpleProfileModels?>(null);

  // Data lists
  final RxList<WidgetProductionDetailModels> arrDataMonthly =
      RxList<WidgetProductionDetailModels>();
  final RxList<WidgetProductionDetailModels> arrDataYearly =
      RxList<WidgetProductionDetailModels>();

  // Filtered data for search
  final RxList<WidgetProductionDetailModels> filteredDataMonthly =
      RxList<WidgetProductionDetailModels>();
  final RxList<WidgetProductionDetailModels> filteredDataYearly =
      RxList<WidgetProductionDetailModels>();

  // Search state
  final RxString searchText = ''.obs;

  // Minimum search length
  static const int minSearchLength = 3;

  @override
  void onInit() async {
    super.onInit();

    // Initialize user level from SharedPreferences
    await _initUserLevel();

    FBroadcast.instance().register('total_net_ape', (value, callback) {
      totalNetApe.value = value as double;
    });

    // Get agent code from parameters
    final agentCodeParam = Get.parameters['agentCode'];
    if (agentCodeParam != null && agentCodeParam.isNotEmpty) {
      agentCode.value = agentCodeParam;
    }

    // Get production type from parameters and set initial dropdown value
    final productionTypeParam = Get.parameters['productionType'];
    if (productionTypeParam != null && productionTypeParam.isNotEmpty) {
      selectedMonth.value = productionTypeParam;
    }

    // Setup listeners first
    _setupListeners();

    // Initialize data based on selected period
    await getData();
  }

  /// Initialize user level from SharedPreferences
  Future<void> _initUserLevel() async {
    try {
      prefs = await SharedPreferences.getInstance();
      userLevel.value = prefs.getString(kStorageUserLevelComplete) ?? '';
      userChannel.value = prefs.getString(kStorageUserChannel) ?? '';
      printInfo(info: 'User level loaded: ${userLevel.value}');
    } catch (e) {
      printError(info: 'Error loading user level: $e');
      userLevel.value = '';
      userChannel.value = '';
    }
  }

  /// Setup reactive listeners for state changes
  void _setupListeners() {
    // Listen to changes in the selected month/year and fetch new data
    ever(selectedMonth, (_) async {
      _resetSearch();
      await getData();
    });

    // Listen to search text changes
    ever(searchText, (value) {
      if (value.length >= minSearchLength) {
        performSearch(value);
      } else if (value.isEmpty) {
        resetFilteredData();
      }
    });
  }

  /// Reset search when switching time periods
  void _resetSearch() {
    searchText.value = '';
    resetFilteredData();
  }

  /// Fetch production data for the specific agent based on selected period
  Future<void> getData() async {
    try {
      // Parameters for API calls
      // Use current year and month for real data
      final year = DateTime.now().year.toString();
      final month = DateTime.now().month.toString();

      setLoading(true);

      // Fetch agent profile first if not already loaded
      if (agentCode.value.isNotEmpty) {
        await api.getSimpleProfile(
          controllers: this,
          agentCode: agentCode.value,
          code: kReqGetSimpleProfile,
        );
      }

      // Fetch data based on selected type
      if (selectedMonth.value == kSwitchMonthly) {
        final paramsMonthly =
            'year=$year&month=$month&agentCode=${agentCode.value}';
        await api.getWidgetProductionDetail(
          controllers: this,
          params: paramsMonthly,
          code: kReqWidgetProductionMonth,
        );
      } else {
        final paramsYearly = 'year=$year&agentCode=${agentCode.value}';
        await api.getWidgetProductionDetail(
          controllers: this,
          params: paramsYearly,
          code: kReqWidgetProductionYear,
        );
      }
    } catch (e) {
      printError(info: 'Error fetching production data: $e');
      setLoading(false);
      update();
    }
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    if (requestCode == kReqGetSimpleProfile) {
      parseSimpleProfile(response);
    } else {
      parseData(response, requestCode: requestCode);
    }
  }

  /// Parse simple profile API response
  void parseSimpleProfile(dynamic response) {
    try {
      agentProfile.value = SimpleProfileModels.fromJson(response);
      printInfo(info: 'Agent profile loaded: ${agentProfile.value?.agentName}');
    } catch (e) {
      printError(info: 'Error parsing simple profile: $e');
      agentProfile.value = null;
    }
  }

  /// Parse API response data
  void parseData(dynamic response, {required int requestCode}) {
    try {
      // Clear the appropriate list based on request code
      _clearDataListForRequestCode(requestCode);

      // Check if response is an object and has 'content' key, otherwise use response directly
      final data = _extractDataFromResponse(response);

      if (data == null || data.isEmpty) {
        printInfo(info: 'No data found for request code: $requestCode');
        resetFilteredData();
        return;
      }

      // Parse data and add to appropriate list
      _addDataToAppropriateList(data, requestCode);

      // Initialize filtered data with all data
      resetFilteredData();
    } catch (e) {
      printError(info: 'Error parsing data: $e');
    } finally {
      setLoading(false);
    }
  }

  /// Clear the appropriate data list based on request code
  void _clearDataListForRequestCode(int requestCode) {
    if (requestCode == kReqWidgetProductionMonth) {
      arrDataMonthly.clear();
    } else if (requestCode == kReqWidgetProductionYear) {
      arrDataYearly.clear();
    }
  }

  /// Extract data from response
  dynamic _extractDataFromResponse(dynamic response) {
    return response is Map && response.containsKey('content')
        ? response['content']
        : response;
  }

  /// Add parsed data to the appropriate list
  void _addDataToAppropriateList(dynamic data, int requestCode) {
    for (int i = 0; i < data.length; i++) {
      try {
        final item = WidgetProductionDetailModels.fromJson(data[i]);

        if (requestCode == kReqWidgetProductionMonth) {
          arrDataMonthly.add(item);
        } else if (requestCode == kReqWidgetProductionYear) {
          arrDataYearly.add(item);
        }
      } catch (e) {
        printError(info: 'Error parsing item at index $i: $e');
      }
    }
  }

  /// Calculate total net APE for monthly data
  num calculateTotalNetApeMonthly() {
    return _calculateTotalNetApe(arrDataMonthly);
  }

  /// Calculate total net APE for yearly data
  num calculateTotalNetApeYearly() {
    return _calculateTotalNetApe(arrDataYearly);
  }

  /// Helper method to calculate total net APE from a list
  num _calculateTotalNetApe(List<WidgetProductionDetailModels> dataList) {
    num total = 0;
    for (var item in dataList) {
      total += item.netApe ?? 0;
    }
    return total;
  }

  /// Get the appropriate total net APE based on current selection
  num getCurrentTotalNetApe() {
    return selectedMonth.value == kSwitchMonthly
        ? calculateTotalNetApeMonthly()
        : calculateTotalNetApeYearly();
  }

  /// Get the current filtered data based on selected period
  List<WidgetProductionDetailModels> getCurrentFilteredData() {
    return selectedMonth.value == kSwitchMonthly
        ? filteredDataMonthly
        : filteredDataYearly;
  }

  /// Reset filtered data to show all data for the current period
  void resetFilteredData() {
    if (selectedMonth.value == kSwitchMonthly) {
      filteredDataMonthly.clear();
      filteredDataMonthly.addAll(arrDataMonthly);
    } else {
      filteredDataYearly.clear();
      filteredDataYearly.addAll(arrDataYearly);
    }

    update();
  }

  /// Perform search on the data for the current period
  void performSearch(String query) {
    if (query.length < minSearchLength) return;

    final searchQuery = query.toLowerCase();

    // Filter data based on current period
    if (selectedMonth.value == kSwitchMonthly) {
      filteredDataMonthly.value = _filterData(arrDataMonthly, searchQuery);
    } else {
      filteredDataYearly.value = _filterData(arrDataYearly, searchQuery);
    }

    update();
  }

  /// Filter data based on search query
  List<WidgetProductionDetailModels> _filterData(
    List<WidgetProductionDetailModels> dataList,
    String searchQuery,
  ) {
    return dataList
        .where((item) => item.containsSearchQuery(searchQuery))
        .toList();
  }

  /// Handle search text change
  void onSearchTextChanged(String value) {
    searchText.value = value;
  }

  /// Get page title based on agent level
  String getPageTitle() {
    final agentLevel = agentProfile.value?.agentLevel ?? '';
    return 'Produksi Individu $agentLevel';
  }
}
