import 'dart:convert';
import 'dart:developer';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PendingPhotoService extends GetxService {
  static const String _pendingPhotosKey = 'pending_photos';
  late SharedPreferences _prefs;
  
  // Observable list of pending photos
  final RxList<PendingPhoto> pendingPhotos = <PendingPhoto>[].obs;
  
  @override
  Future<void> onInit() async {
    super.onInit();
    _prefs = await SharedPreferences.getInstance();
    await loadPendingPhotos();
  }

  /// Load pending photos from SharedPreferences
  Future<void> loadPendingPhotos() async {
    try {
      final String? photosJson = _prefs.getString(_pendingPhotosKey);
      if (photosJson != null) {
        final List<dynamic> photosList = json.decode(photosJson);
        pendingPhotos.value = photosList
            .map((json) => PendingPhoto.fromJson(json))
            .toList();
        log('Loaded ${pendingPhotos.length} pending photos from storage');
      }
    } catch (e) {
      log('Error loading pending photos: $e');
      pendingPhotos.clear();
    }
  }

  /// Save pending photos to SharedPreferences
  Future<void> savePendingPhotos() async {
    try {
      final String photosJson = json.encode(
        pendingPhotos.map((photo) => photo.toJson()).toList(),
      );
      await _prefs.setString(_pendingPhotosKey, photosJson);
      log('Saved ${pendingPhotos.length} pending photos to storage');
    } catch (e) {
      log('Error saving pending photos: $e');
    }
  }

  /// Add a new pending photo
  Future<void> addPendingPhoto({
    required String localPath,
    required String type,
    required String formId,
    String? fileName,
  }) async {
    try {
      // Check if photo already exists
      final existingIndex = pendingPhotos.indexWhere(
        (photo) => photo.localPath == localPath && photo.type == type,
      );

      final pendingPhoto = PendingPhoto(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        localPath: localPath,
        type: type,
        formId: formId,
        fileName: fileName ?? localPath.split('/').last,
        createdAt: DateTime.now(),
        uploadAttempts: 0,
        lastAttemptAt: null,
      );

      if (existingIndex >= 0) {
        // Update existing photo
        pendingPhotos[existingIndex] = pendingPhoto;
      } else {
        // Add new photo
        pendingPhotos.add(pendingPhoto);
      }

      await savePendingPhotos();
      log('Added pending photo: $type at $localPath');
    } catch (e) {
      log('Error adding pending photo: $e');
    }
  }

  /// Remove a pending photo (when successfully uploaded)
  Future<void> removePendingPhoto(String id) async {
    try {
      pendingPhotos.removeWhere((photo) => photo.id == id);
      await savePendingPhotos();
      log('Removed pending photo with id: $id');
    } catch (e) {
      log('Error removing pending photo: $e');
    }
  }

  /// Update upload attempt for a photo
  Future<void> updateUploadAttempt(String id, {bool success = false}) async {
    try {
      final index = pendingPhotos.indexWhere((photo) => photo.id == id);
      if (index >= 0) {
        final photo = pendingPhotos[index];
        pendingPhotos[index] = photo.copyWith(
          uploadAttempts: photo.uploadAttempts + 1,
          lastAttemptAt: DateTime.now(),
        );
        
        if (success) {
          await removePendingPhoto(id);
        } else {
          await savePendingPhotos();
        }
      }
    } catch (e) {
      log('Error updating upload attempt: $e');
    }
  }

  /// Get pending photos by form ID
  List<PendingPhoto> getPendingPhotosByFormId(String formId) {
    return pendingPhotos.where((photo) => photo.formId == formId).toList();
  }

  /// Get pending photos by type
  List<PendingPhoto> getPendingPhotosByType(String type) {
    return pendingPhotos.where((photo) => photo.type == type).toList();
  }

  /// Check if a photo is pending upload
  bool isPhotoPending(String localPath, String type) {
    return pendingPhotos.any(
      (photo) => photo.localPath == localPath && photo.type == type,
    );
  }

  /// Get total count of pending photos
  int get pendingCount => pendingPhotos.length;

  /// Check if there are any pending photos
  bool get hasPendingPhotos => pendingPhotos.isNotEmpty;

  /// Clear all pending photos (use with caution)
  Future<void> clearAllPendingPhotos() async {
    try {
      pendingPhotos.clear();
      await _prefs.remove(_pendingPhotosKey);
      log('Cleared all pending photos');
    } catch (e) {
      log('Error clearing pending photos: $e');
    }
  }

  /// Get photos that should be retried (not attempted recently)
  List<PendingPhoto> getPhotosForRetry({int maxAttempts = 3, int cooldownMinutes = 5}) {
    final now = DateTime.now();
    return pendingPhotos.where((photo) {
      // Skip if max attempts reached
      if (photo.uploadAttempts >= maxAttempts) return false;
      
      // Skip if recently attempted (within cooldown period)
      if (photo.lastAttemptAt != null) {
        final timeSinceLastAttempt = now.difference(photo.lastAttemptAt!);
        if (timeSinceLastAttempt.inMinutes < cooldownMinutes) return false;
      }
      
      return true;
    }).toList();
  }
}

class PendingPhoto {
  final String id;
  final String localPath;
  final String type;
  final String formId;
  final String fileName;
  final DateTime createdAt;
  final int uploadAttempts;
  final DateTime? lastAttemptAt;

  PendingPhoto({
    required this.id,
    required this.localPath,
    required this.type,
    required this.formId,
    required this.fileName,
    required this.createdAt,
    required this.uploadAttempts,
    this.lastAttemptAt,
  });

  PendingPhoto copyWith({
    String? id,
    String? localPath,
    String? type,
    String? formId,
    String? fileName,
    DateTime? createdAt,
    int? uploadAttempts,
    DateTime? lastAttemptAt,
  }) {
    return PendingPhoto(
      id: id ?? this.id,
      localPath: localPath ?? this.localPath,
      type: type ?? this.type,
      formId: formId ?? this.formId,
      fileName: fileName ?? this.fileName,
      createdAt: createdAt ?? this.createdAt,
      uploadAttempts: uploadAttempts ?? this.uploadAttempts,
      lastAttemptAt: lastAttemptAt ?? this.lastAttemptAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'localPath': localPath,
      'type': type,
      'formId': formId,
      'fileName': fileName,
      'createdAt': createdAt.toIso8601String(),
      'uploadAttempts': uploadAttempts,
      'lastAttemptAt': lastAttemptAt?.toIso8601String(),
    };
  }

  factory PendingPhoto.fromJson(Map<String, dynamic> json) {
    return PendingPhoto(
      id: json['id'],
      localPath: json['localPath'],
      type: json['type'],
      formId: json['formId'],
      fileName: json['fileName'],
      createdAt: DateTime.parse(json['createdAt']),
      uploadAttempts: json['uploadAttempts'] ?? 0,
      lastAttemptAt: json['lastAttemptAt'] != null 
          ? DateTime.parse(json['lastAttemptAt']) 
          : null,
    );
  }

  @override
  String toString() {
    return 'PendingPhoto(id: $id, type: $type, localPath: $localPath, attempts: $uploadAttempts)';
  }
}
