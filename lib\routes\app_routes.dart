// ignore_for_file: constant_identifier_names

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/pages/authentication/first_login/change_password_success.dart';
import 'package:pdl_superapp/pages/authentication/first_login/first_login_success_page.dart';
import 'package:pdl_superapp/pages/authentication/forgot_password.dart';
import 'package:pdl_superapp/pages/authentication/forgot_password_sent.dart';
import 'package:pdl_superapp/pages/authentication/login_page.dart';
import 'package:pdl_superapp/pages/authentication/first_login/reset_password_page.dart';
import 'package:pdl_superapp/pages/authentication/verification_expired.dart';
import 'package:pdl_superapp/pages/dummy_page.dart';
import 'package:pdl_superapp/pages/error_page.dart';
import 'package:pdl_superapp/pages/home/<USER>';
import 'package:pdl_superapp/pages/home/<USER>';
import 'package:pdl_superapp/pages/home/<USER>';
import 'package:pdl_superapp/pages/main_navigator.dart';
import 'package:pdl_superapp/pages/menu/keagenan/public_recruitment/public_recruitment_form_page.dart';
import 'package:pdl_superapp/pages/menu/keagenan/public_recruitment/public_success_page.dart';
import 'package:pdl_superapp/pages/menu/keagenan/public_recruitment/public_terms_only_page.dart';
import 'package:pdl_superapp/pages/menu/keagenan/public_recruitment/public_email_verification_page.dart';
import 'package:pdl_superapp/pages/menu/keagenan/recruitment/approval_page.dart';
import 'package:pdl_superapp/pages/menu/keagenan/recruitment/form_interview.dart';
import 'package:pdl_superapp/pages/menu/keagenan/recruitment/recruitment_form_page.dart';
import 'package:pdl_superapp/pages/menu/keagenan/photo_page.dart';
import 'package:pdl_superapp/middlewares/auth_middleware.dart';
import 'package:pdl_superapp/pages/menu/inbox/detail_inbox_page.dart';
import 'package:pdl_superapp/pages/menu/inbox/inbox_list_page.dart';
import 'package:pdl_superapp/pages/menu/keagenan/keagenan_page.dart';
import 'package:pdl_superapp/pages/menu/keagenan/photo_image_cropper_page.dart';
import 'package:pdl_superapp/pages/menu/keagenan/photo_page_panduan.dart';
import 'package:pdl_superapp/pages/menu/keagenan/recruitment/recruitment_list_page.dart';
import 'package:pdl_superapp/pages/menu/keagenan/terminasi/approval_terminasi_page.dart';
import 'package:pdl_superapp/pages/rejoin/choose_user_rejoin_list_page.dart';
import 'package:pdl_superapp/pages/terminasi/approval_polis_assignment_list.dart';
import 'package:pdl_superapp/pages/terminasi/approval_polis_assignment_page.dart';
import 'package:pdl_superapp/pages/terminasi/reassign_polis_list.dart';
import 'package:pdl_superapp/pages/terminasi/reassign_polis_page.dart';
import 'package:pdl_superapp/pages/terminasi/terminasi_bp_page.dart';
import 'package:pdl_superapp/pages/terminasi/terminasi_detail_page.dart';
import 'package:pdl_superapp/pages/terminasi/terminasi_request_list.dart';
import 'package:pdl_superapp/pages/terminasi/terminasi_page.dart';
import 'package:pdl_superapp/pages/terminasi/terminasi_select_team.dart';
import 'package:pdl_superapp/pages/rejoin/detail_rejoin_page.dart';
import 'package:pdl_superapp/pages/rejoin/rejoin_list_page.dart';
import 'package:pdl_superapp/pages/tutorial/tutorial_page.dart';
import 'package:pdl_superapp/pages/widget/page_wrapper.dart';
import 'package:pdl_superapp/pages/widget/production_individu_detail_widget_page.dart';
import 'package:pdl_superapp/pages/widget/public_page_wrapper.dart';
import 'package:pdl_superapp/pages/profile/change_password_page.dart';
import 'package:pdl_superapp/pages/profile/device_page.dart';
import 'package:pdl_superapp/pages/profile/profile_edit_page.dart';
import 'package:pdl_superapp/pages/profile/profile_page.dart';
import 'package:pdl_superapp/pages/profile/qna_page.dart';
import 'package:pdl_superapp/pages/profile/qna_detail_page.dart';
import 'package:pdl_superapp/pages/profile/setting_page.dart';
import 'package:pdl_superapp/pages/read_page.dart';
import 'package:pdl_superapp/pages/widget/spaj_page.dart';
import 'package:pdl_superapp/pages/widget/production_widget_page.dart';
import 'package:pdl_superapp/pages/widget/production_detail_widget_page.dart';
import 'package:pdl_superapp/pages/widget/kompensasi_page.dart';
import 'package:pdl_superapp/pages/widget/claim_page.dart';
import 'package:pdl_superapp/pages/widget/polis_lapse_page.dart';
import 'package:pdl_superapp/pages/widget/polis_jatuh_tempo_page.dart';
import 'package:pdl_superapp/pages/widget/birthday_page.dart';
import 'package:pdl_superapp/pages/home/<USER>';
import 'package:pdl_superapp/pages/notification_test_page.dart';
import 'package:pdl_superapp/pages/widget/validasi_detail_page.dart';

import '../pages/widget/persistensi_page.dart';

abstract class Routes {
  Routes._();

  static const MAIN = '/main';
  static const HOME = '/home';
  static const LOGIN = '/login';
  static const FIRST_LOGIN_SUCCESS = '/login-success-first';
  static const RESET_PASSWORD = '/new-password';
  static const CHANGE_PASSWORD_SUCCESS = '/change-password-success';
  static const FORGOT_PASSWORD = '/forget-password';
  static const FORGOT_PASSWORD_SENT = '/forgot-password-sent';
  static const VERIFICATION_EXPIRED = '/verification-expired';
  static const READ_PAGE = '/read';
  static const NOT_FOUND = '/404';

  // Page
  static const PROFILE = '/profile';
  static const PROFILE_EDIT = '/profile-edit';
  static const CHANGE_PASSWORD = '/change-password';
  static const SETTING = '/setting';
  static const CONNECTED_DEVICES = '/connected-devices';
  static const QNA = '/qna';
  static const QNA_DETAIL = '/qna/:id';
  static const TERMINASI = '/terminasi';
  static const TERMINASI_DETAIL = '/terminasi-detail';
  static const TERMINASI_BP = '/terminasi-bp';
  static const TERMINASI_SELECT_TEAM = '/terminasi-select-team';
  static const TERMINASI_LIST = '/terminasi-list';
  static const APPROVAL_POLIS_ASSIGNMENT_PAGE =
      '/approval-polis-assignment-page';
  static const APPROVAL_POLIS_ASSIGNMENT_LIST =
      '/approval-polis-assignment-list';
  static const REASSIGN_POLIS_PAGE = '/reassign-polis-page';
  static const REASSIGN_POLIS_LIST = '/reassign-polis-list';

  // Widget Page
  static const WIDGET_MY_PRODUCTION = '/production';
  static const PRODUCTION_DETAIL = '/production/detail';
  static const KOMPENSASI = '/kompensasi';
  static const CLAIM = '/claim';
  static const POLIS_LAPSE = '/polis-lapse';
  static const POLIS_JATUH_TEMPO = '/polis-jatuh-tempo';
  static const BIRTHDAY = '/birthday';
  static const FAVORITE = '/favorite';
  static const SPAJ = '/spaj';
  static const PERSISTENSI = '/persistensi';
  static const VALIDASI_DETAIL = '/validasi/detail';
  static const PRODUCTION_INDIVIDU_DETAIL = '/production/individu/detail';

  // Menu Page
  // -- Keagenan
  static const MENU_KEAGENAN = '/keagenan';
  static const KEAGENAN_LIST = '/list-keagenan';
  static const KEAGENAN_FORM = '/form-keagenan';
  static const APPROVAL = '/approval-keagenan';
  static const FORM_INTERVIEW = '/form-interview';
  static const APPROVAL_TERMINASI = '/approval-terminasi';

  static const ROOT = '/';
  static const PHOTO_PAGE = '/photo-page';
  static const PHOTO_PAGE_PANDUAN = '/photo-page-panduan';
  static const PHOTO_IMAGE_CROPPER = '/photo-image-cropper';
  static const KTP_PAGE = '/ktp-page';
  static const KTP_PAGE_PANDUAN = '/ktp-page-panduan';
  static const KTP_IMAGE_CROPPER = '/ktp-image-cropper';

  // --- rejoin
  static const REJOIN = '/rejoin';
  static const REJOIN_CHOOSE_USER = '/rejoin/choose';
  static const DETAIL_REJOIN = '/rejoin/detail';

  // ---

  // --- inbox
  static const MENU_INBOX = '/inbox';
  static const MENU_DETAIL_INBOX = '/inbox/detail';

  //---

  // --- notification
  static const NOTIFICATION_PAGE = '/notification-page';
  static const NOTIFICATION_DETAIL_PAGE = '/notification-page-detail';
  static const NOTIFICATION_TEST = '/notification-test';

  //---

  // Public pages
  static const PUBLIC_RECRUITMENT_FORM = '/public/form-keagenan';
  static const PUBLIC_TTD_ONLY = '/public/signature-only';
  static const PUBLIC_SUCCESS = '/public/success';
  static const PUBLIC_EMAIL_VERIFICATION = '/public/verification/:token';

  static const TUTORIAL = '/tutorial';

  // for early developemtn
  static const DUMMYPAGE = '/dummy-page';
}

class AppPages {
  AppPages._();

  static final routes = [
    // Root route - redirect to login
    GetPage(
      name: Routes.ROOT,
      page: () => PageWrapper(child: MainNavigatorScreen()),
      transition: Transition.noTransition,
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: Routes.DUMMYPAGE,
      page: () => PageWrapper(child: Dummypage()),
      transition: Transition.noTransition,
      // middlewares: [
      //   PublicLayoutMiddleware(),
      //   LoginGuardMiddleware(), // Redirect to home if already logged in
      // ],
    ),
    GetPage(
      name: Routes.MAIN,
      page: () => PageWrapper(child: MainNavigatorScreen()),
      transition: Transition.noTransition,
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: Routes.HOME,
      page: () => PageWrapper(child: HomePage()),
      transition: Transition.noTransition,
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: Routes.PROFILE,
      page: () => PageWrapper(child: ProfilePage()),
      transition: Transition.noTransition,
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: Routes.PROFILE_EDIT,
      page: () => PageWrapper(child: ProfileEditPage()),
      transition: Transition.noTransition,
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: Routes.CHANGE_PASSWORD,
      page: () => PageWrapper(child: ChangePasswordPage()),
      transition: Transition.noTransition,
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: Routes.SETTING,
      page: () => PageWrapper(child: SettingPage()),
      transition: Transition.noTransition,
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: Routes.CONNECTED_DEVICES,
      page: () => PageWrapper(child: ConnectedDevicePage()),
      transition: Transition.noTransition,
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: Routes.LOGIN,
      page: () => PageWrapper(child: LoginPage()),
      transition: Transition.noTransition,
      middlewares: [LoginGuardMiddleware()],
    ),
    GetPage(
      name: Routes.FIRST_LOGIN_SUCCESS,
      page: () => PageWrapper(child: FirstLoginSuccessPage()),
      transition: Transition.noTransition,
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: Routes.RESET_PASSWORD,
      page: () => PageWrapper(child: ResetPasswordPage()),
      transition: Transition.noTransition,
      middlewares: [LoginGuardMiddleware(), PublicMiddleware()],
    ),
    GetPage(
      name: Routes.CHANGE_PASSWORD_SUCCESS,
      page: () => PageWrapper(child: ChangePasswordSuccessPage()),
      transition: Transition.noTransition,
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: Routes.FORGOT_PASSWORD,
      page: () => PageWrapper(child: ForgotPasswordPage()),
      transition: Transition.noTransition,
      middlewares: [LoginGuardMiddleware()],
    ),
    GetPage(
      name: Routes.FORGOT_PASSWORD_SENT,
      page: () => PageWrapper(child: ForgotPasswordSentPage()),
      transition: Transition.noTransition,
      middlewares: [LoginGuardMiddleware()],
    ),
    GetPage(
      name: Routes.VERIFICATION_EXPIRED,
      page: () => PageWrapper(child: VerificationExpiredPage()),
      transition: Transition.noTransition,
      middlewares: [LoginGuardMiddleware()],
    ),
    GetPage(
      name: Routes.READ_PAGE,
      page: () => PageWrapper(child: ReadPage()),
      transition: Transition.noTransition,
      middlewares: [PublicMiddleware()],
    ),

    GetPage(
      name: Routes.QNA,
      page: () => PageWrapper(child: QnaPage()),
      transition: Transition.noTransition,
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: Routes.QNA_DETAIL,
      page: () => PageWrapper(child: QnaDetailPage()),
      transition: Transition.noTransition,
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: Routes.KOMPENSASI,
      page: () => PageWrapper(child: KompensasiPage()),
      transition: Transition.noTransition,
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: Routes.WIDGET_MY_PRODUCTION,
      page: () => PageWrapper(child: ProductionWidgetPage()),
      transition: Transition.noTransition,
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: Routes.PRODUCTION_DETAIL,
      page: () => PageWrapper(child: ProductionDetailWidgetPage()),
      transition: Transition.noTransition,
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: Routes.PRODUCTION_INDIVIDU_DETAIL,
      page: () => PageWrapper(child: ProductionIndividuDetailWidgetPage()),
      transition: Transition.noTransition,
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: Routes.CLAIM,
      page: () => PageWrapper(child: ClaimPage()),
      transition: Transition.noTransition,
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: Routes.POLIS_LAPSE,
      page: () => PageWrapper(child: PolisLapsePage()),
      transition: Transition.noTransition,
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: Routes.POLIS_JATUH_TEMPO,
      page: () => PageWrapper(child: PolisJatuhTempoPage()),
      transition: Transition.noTransition,
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: Routes.BIRTHDAY,
      page: () => PageWrapper(child: BirthdayPage()),
      transition: Transition.noTransition,
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: Routes.FAVORITE,
      page: () => PageWrapper(child: FavoriteWidgetPage()),
      transition: Transition.noTransition,
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: Routes.PERSISTENSI,
      page: () => PageWrapper(child: PersistensiPage()),
      transition: Transition.noTransition,
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: Routes.SPAJ,
      page: () => PageWrapper(child: SpajPage()),
      transition: Transition.noTransition,
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: Routes.TERMINASI,
      page: () => PageWrapper(child: TerminasiPage()),
      transition: Transition.noTransition,
    ),
    GetPage(
      name: Routes.TERMINASI_DETAIL,
      page: () => PageWrapper(child: TerminasiDetailPage()),
      transition: Transition.noTransition,
    ),
    GetPage(
      name: Routes.TERMINASI_BP,
      page: () => PageWrapper(child: TerminasiBpPage()),
      transition: Transition.noTransition,
    ),
    GetPage(
      name: Routes.TERMINASI_SELECT_TEAM,
      page: () => PageWrapper(child: TerminasiSelectTeam()),
      transition: Transition.noTransition,
    ),
    GetPage(
      name: Routes.TERMINASI_LIST,
      page: () => PageWrapper(child: TerminasiRequestList()),
      transition: Transition.noTransition,
    ),
    GetPage(
      name: Routes.APPROVAL_POLIS_ASSIGNMENT_PAGE,
      page: () => PageWrapper(child: ApprovalPolisAssignmentPage()),
      transition: Transition.noTransition,
    ),
    GetPage(
      name: Routes.APPROVAL_POLIS_ASSIGNMENT_LIST,
      page: () => PageWrapper(child: ApprovalPolisAssignmentList()),
      transition: Transition.noTransition,
    ),
    GetPage(
      name: Routes.REASSIGN_POLIS_LIST,
      page: () => PageWrapper(child: ReassignPolisList()),
      transition: Transition.noTransition,
    ),
    GetPage(
      name: Routes.REASSIGN_POLIS_PAGE,
      page: () => PageWrapper(child: ReassignPolisPage()),
      transition: Transition.noTransition,
    ),
    GetPage(
      name: Routes.VALIDASI_DETAIL,
      page: () => PageWrapper(child: ValidasiDetailPage()),
      transition: Transition.noTransition,
      middlewares: [AuthMiddleware()],
    ),

    // Menu
    GetPage(
      name: Routes.MENU_KEAGENAN,
      page: () => PageWrapper(child: KeagenanPage()),
      transition: Transition.noTransition,
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: Routes.KEAGENAN_LIST,
      page: () => PageWrapper(child: RecruitmentListPage()),
      transition: Transition.noTransition,
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: Routes.KEAGENAN_FORM,
      page: () => PageWrapper(child: RecruitmentFormPage()),
      transition: Transition.noTransition,
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: Routes.PHOTO_PAGE,
      page: () => PageWrapper(child: PhotoPage()),
      transition: Transition.noTransition,
      middlewares: [
        // AuthMiddleware()
        PublicMiddleware(),
      ],
    ),
    GetPage(
      name: Routes.PHOTO_PAGE_PANDUAN,
      page: () => PageWrapper(child: PhotoPagePanduan()),
      transition: Transition.noTransition,
      middlewares: [
        // AuthMiddleware()
        PublicMiddleware(),
      ],
    ),
    GetPage(
      name: Routes.PHOTO_IMAGE_CROPPER,
      page: () => PageWrapper(child: PhotoImageCropperPage()),
      transition: Transition.noTransition,
      middlewares: [
        // AuthMiddleware()
        PublicMiddleware(),
      ],
    ),
    GetPage(
      name: Routes.REJOIN,
      page: () => PageWrapper(child: RejoinListPage()),
      transition: Transition.noTransition,
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: Routes.REJOIN_CHOOSE_USER,
      page: () => PageWrapper(child: ChooseUserRejoinListPage()),
      transition: Transition.noTransition,
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: Routes.DETAIL_REJOIN,
      page: () => PageWrapper(child: DetailRejoinPage()),
      transition: Transition.noTransition,
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: Routes.MENU_INBOX,
      page: () => PageWrapper(child: InboxListPage()),
      transition: Transition.downToUp,
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: Routes.MENU_DETAIL_INBOX,
      page: () => PageWrapper(child: DetailInboxPage()),
      transition: Transition.rightToLeft,
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: Routes.APPROVAL,
      page: () => PageWrapper(child: ApprovalPage()),
      transition: Transition.noTransition,
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: Routes.APPROVAL_TERMINASI,
      page: () => PageWrapper(child: ApprovalTerminasiPage()),
      transition: Transition.noTransition,
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: Routes.FORM_INTERVIEW,
      page: () => PageWrapper(child: FormInterviewPage()),
      transition: Transition.noTransition,
      middlewares: [AuthMiddleware()],
    ),

    // Public Pages
    GetPage(
      name: Routes.PUBLIC_RECRUITMENT_FORM,
      page: () => PublicPageWrapper(child: PublicRecruitmentFormPage()),
      transition: Transition.noTransition,
      middlewares: [PublicMiddleware()],
    ),

    GetPage(
      name: Routes.PUBLIC_TTD_ONLY,
      page: () => PublicPageWrapper(child: PublicFormTermsSignatureOnly()),
      transition: Transition.noTransition,
      middlewares: [PublicMiddleware()],
    ),

    GetPage(
      name: Routes.PUBLIC_SUCCESS,
      page: () => PublicPageWrapper(child: PublicSuccessPage()),
      transition: Transition.noTransition,
      middlewares: [PublicMiddleware()],
    ),

    GetPage(
      name: Routes.PUBLIC_EMAIL_VERIFICATION,
      page: () => PublicEmailVerificationPage(),
      transition: Transition.noTransition,
      middlewares: [PublicMiddleware()],
    ),

    // Notification Test Page
    GetPage(
      name: Routes.NOTIFICATION_TEST,
      page: () => PageWrapper(child: NotificationTestPage()),
      transition: Transition.noTransition,
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: Routes.NOTIFICATION_PAGE,
      page: () => PageWrapper(child: NotificationPage()),
      transition: Transition.noTransition,
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: Routes.NOTIFICATION_DETAIL_PAGE,
      page: () => PageWrapper(child: NotificationDetailPage()),
      transition: Transition.noTransition,
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: Routes.TUTORIAL,
      page: () => PageWrapper(child: TutorialPage()),
      transition: Transition.noTransition,
      middlewares: [AuthMiddleware()],
    ),
  ];

  static Route<dynamic> unknownRoute(RouteSettings settings) {
    return GetPageRoute(
      settings: settings,
      page: () => const ErrorPage(message: "Page not found"),
      transition: Transition.noTransition,
    );
  }
}
