import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/image_form.dart';
import 'package:pdl_superapp/components/pdl_base_dialog.dart';
import 'package:pdl_superapp/components/pdl_drop_down.dart';
import 'package:pdl_superapp/components/pdl_text_field.dart';
import 'package:pdl_superapp/components/title_widget.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/form_verification_controller.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:shimmer/shimmer.dart';

class FormVerification extends StatelessWidget {
  final FormVerificationController controller;

  const FormVerification({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (controller.baseController.isFormDisabled.isFalse)
          TitleWidget(title: 'Informasi Perekrut'),
        if (controller.baseController.isFormDisabled.isFalse)
          SizedBox(height: paddingMedium),
        if (controller.baseController.isFormDisabled.isFalse)
          _recruiterInformation(context),
        if (controller.baseController.isFormDisabled.isFalse)
          SizedBox(height: paddingSmall),
        if (controller.baseController.isFormDisabled.isFalse) Divider(),
        SizedBox(height: paddingMedium),
        if (controller.baseController.isFormDisabled.isFalse)
          _candidateLevel(context),
        if (controller.baseController.isFormDisabled.isFalse)
          SizedBox(height: paddingLarge),
        _cadidateIdentity(context),
      ],
    );
  }

  SizedBox _cadidateIdentity(BuildContext context) {
    return SizedBox(
      width: Get.width,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TitleWidget(title: 'Foto Identitas Kandidat'),
          SizedBox(height: paddingSmall),
          Text(
            'Pastikan foto yang diunggah telah sesuai dengan ketentuan perusahaan.',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          SizedBox(height: paddingMedium),
          _imageContainer(
            context,
            Obx(
              () => ImageForm(
                title: 'Foto KTP',
                isDisabled: controller.baseController.isFormDisabled.value,
                imageFile: controller.ktpImage,
                imageUrl: controller.ktpUrl,
                onTapAdd: () => controller.pickKtpImage('KTP', kPhotoTypeKtp),
                description: 'Identitas sesuai KTP akan terisi secara otomatis',
                uploadText: 'Unggah atau ambil foto KTP',
                isUploading: controller.isKtpUploading,
                uploadProgress: controller.ktpUploadProgress,
                onClear: () => controller.clearImage(kPhotoTypeKtp),
                hasError: controller.ktpImageError.value.isNotEmpty,
                errorText:
                    controller.ktpImageError.value.isEmpty
                        ? null
                        : controller.ktpImageError.value,
                photoType: kPhotoTypeKtp,
              ),
            ),
            imageUrl: controller.ktpUrl.value,
          ),
          SizedBox(height: paddingMedium),
          _imageContainer(
            context,
            Obx(
              () => ImageForm(
                title: 'Selfie Dengan KTP',
                imageFile: controller.selfieKtpImage,
                isDisabled: controller.baseController.isFormDisabled.value,
                imageUrl: controller.selfieKtpUrl,
                onTapAdd:
                    () => controller.pickKtpImage(
                      'Selfie Dengan KTP',
                      kPhotoTypeSelfieKtp,
                    ),
                uploadText: 'Unggah atau ambil foto selfie dengan KTP',
                isUploading: controller.isSelfieKtpUploading,
                uploadProgress: controller.selfieKtpUploadProgress,
                onClear: () => controller.clearImage(kPhotoTypeSelfieKtp),
                hasError: controller.selfieKtpImageError.value.isNotEmpty,
                errorText:
                    controller.selfieKtpImageError.value.isEmpty
                        ? null
                        : controller.selfieKtpImageError.value,
                photoType: kPhotoTypeSelfieKtp,
              ),
            ),
            imageUrl: controller.selfieKtpUrl.value,
          ),
          SizedBox(height: paddingMedium),
          _imageContainer(
            context,
            Obx(
              () => ImageForm(
                title: 'Pas Foto',
                imageFile: controller.pasFotoImage,
                isDisabled: controller.baseController.isFormDisabled.value,
                imageUrl: controller.pasFotoUrl,
                onTapAdd:
                    () =>
                        controller.pickKtpImage('Pas Foto', kPhotoTypePasFoto),
                uploadText: 'Unggah atau ambil pas foto',
                isUploading: controller.isPasFotoUploading,
                uploadProgress: controller.pasFotoUploadProgress,
                onClear: () => controller.clearImage(kPhotoTypePasFoto),
                hasError: controller.pasFotoImageError.value.isNotEmpty,
                errorText:
                    controller.pasFotoImageError.value.isEmpty
                        ? null
                        : controller.pasFotoImageError.value,
                photoType: kPhotoTypePasFoto,
              ),
            ),
            imageUrl: controller.pasFotoUrl.value,
          ),
        ],
      ),
    );
  }

  GestureDetector _imageContainer(
    BuildContext context,
    Widget child, {
    required imageUrl,
  }) {
    return GestureDetector(
      onTap: () {
        if (controller.baseController.isFormDisabled.value != true) {
          return;
        }
        PdlBaseDialog(
          context: context,
          child: Utils.cachedImageWrapper(imageUrl, isFullUrl: true),
        );
      },
      child: child,
    );
  }

  Widget _candidateLevel(BuildContext context) {
    return Obx(() {
      if (controller.isAgentLoading.value == true) {
        return Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Container(
            width: Get.width,
            height: 40,
            decoration: BoxDecoration(
              color: kColorBgLight,
              borderRadius: BorderRadius.circular(radiusSmall),
            ),
          ),
        );
      } else {
        return SizedBox(
          width: Get.width,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TitleWidget(title: 'Level Keagenan Kandidat'),
              SizedBox(height: paddingMedium),
              Obx(() {
                if (controller.state.value == ControllerState.loading) {
                  return CircularProgressIndicator();
                }
                return PdlDropDown(
                  item: [
                    for (int i = 0; i < controller.roleCandidate.length; i++)
                      controller.roleCandidate[i],
                  ],
                  selectedItem:
                      controller.candidateLevelController.text != ''
                          ? controller.candidateLevelController.text
                          : null,
                  title: 'Level',
                  enabled: !controller.baseController.isFormDisabled.value,
                  onChanged: (val) {
                    if (val != null) {
                      controller.candidateLevelController.text = val;
                    }
                  },
                  disableSearch: true,
                );
              }),
              SizedBox(height: paddingMedium),
              Obx(
                () => PdlTextField(
                  textController: controller.candidateBranchController,
                  hint: 'Cari Kantor Cabang',
                  label: 'Kantor Cabang',
                  onChanged: (val) async {
                    Future.delayed(Duration(microseconds: 300)).then((x) {
                      controller.onBranchTextChanged(val);
                    });
                  },
                  onEditingComplete: () {
                    // Validate branch selection when user finishes editing
                    controller.validateBranchSelectionRealTime();
                  },
                  prefixIcon: Icon(Icons.search),
                  hasError: controller.candidateBranchError.value.isNotEmpty,
                  errorText:
                      controller.candidateBranchError.value.isEmpty
                          ? null
                          : controller.candidateBranchError.value,
                  items: [
                    // Only show branch list if user has typed something
                    if (controller.candidateBranchText.value.isNotEmpty)
                      for (int i = 0; i < controller.branchList.length; i++)
                        GestureDetector(
                          onTap: () {
                            controller.candidateBranchController.text =
                                controller.branchList[i].branchName ?? '-';
                            controller.candidateBranchText.value =
                                controller.branchList[i].branchName ?? '-';
                            controller.candidateBranchCode.value =
                                controller.branchList[i].id ?? 0;
                            controller.branchList.clear();
                            // Clear error when user selects a valid branch
                            controller.candidateBranchError.value = '';
                            // Unfocus the text field after selection
                            FocusScope.of(context).unfocus();
                          },
                          child: Container(
                            width: Get.width,
                            color: Colors.transparent,
                            padding: EdgeInsets.only(top: paddingSmall),
                            child: Text(
                              controller.branchList[i].branchName ?? '-',
                            ),
                          ),
                        ),
                  ],
                ),
              ),
            ],
          ),
        );
      }
    });
  }

  Widget _recruiterInformation(BuildContext context) {
    return Obx(() {
      if (controller.state.value == ControllerState.loading) {
        return CircularProgressIndicator();
      }
      return Row(
        children: [
          SizedBox(
            width: 50,
            height: 50,
            child: CircleAvatar(
              backgroundImage:
                  controller.recruiterPhoto.value.isNotEmpty
                      ? NetworkImage(controller.recruiterPhoto.value)
                          as ImageProvider
                      : null,
              child:
                  controller.recruiterPhoto.value.isEmpty
                      ? Text(Utils.getInitials(controller.recruiterName.value))
                      : null,
            ),
          ),
          SizedBox(width: paddingSmall),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Wrap(
                  crossAxisAlignment: WrapCrossAlignment.center,
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: kColorGlobalBgBlue,
                        borderRadius: BorderRadius.circular(paddingMedium),
                      ),
                      padding: EdgeInsets.all(paddingSmall),
                      child: Text(
                        controller.recruiterLevel.value,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: kColorGlobalBlue,
                        ),
                      ),
                    ),
                    SizedBox(width: paddingSmall),
                    Text(
                      controller.recruiterName.value,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ],
                ),
                SizedBox(
                  width: Get.width,
                  child: RichText(
                    text: TextSpan(
                      children: [
                        TextSpan(text: controller.recruiterCode.value),
                        if (controller.recruiterBranch.value != '-')
                          TextSpan(text: ' - '),
                        if (controller.recruiterBranch.value != '-')
                          TextSpan(text: controller.recruiterBranch.value),
                      ],
                      style: Theme.of(
                        context,
                      ).textTheme.bodyMedium?.copyWith(height: 2),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    });
  }
}
