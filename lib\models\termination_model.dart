class TerminationModel {
  TerminationModel({
    required this.id,
    required this.reason,
    required this.status,
    required this.approvalStatus,
    required this.requestedBy,
    required this.target,
    required this.createdAt,
    required this.updatedAt,
    required this.approvalHeader,
    required this.policyTransferDto,
  });

  final int? id;
  final String? reason;
  final String? status;
  final String? approvalStatus;
  final RequestedBy? requestedBy;
  final RequestedBy? target;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final ApprovalHeader? approvalHeader;
  final PolicyTransferDto? policyTransferDto;

  factory TerminationModel.fromJson(Map<String, dynamic> json) {
    return TerminationModel(
      id: json["id"],
      reason: json["reason"],
      status: json["status"],
      approvalStatus: json["approvalStatus"],
      requestedBy:
          json["requestedBy"] == null
              ? null
              : RequestedBy.fromJson(json["requestedBy"]),
      target:
          json["target"] == null ? null : RequestedBy.fromJson(json["target"]),
      createdAt: DateTime.tryParse(json["createdAt"] ?? ""),
      updatedAt: DateTime.tryParse(json["updatedAt"] ?? ""),
      approvalHeader:
          json["approvalHeader"] == null
              ? null
              : ApprovalHeader.fromJson(json["approvalHeader"]),
      policyTransferDto:
          json["policyTransferDto"] == null
              ? null
              : PolicyTransferDto.fromJson(json["policyTransferDto"]),
    );
  }
}

class ApprovalHeader {
  ApprovalHeader({
    required this.id,
    required this.requestId,
    required this.requestBy,
    required this.trxType,
    required this.trxId,
    required this.approvalStatus,
    required this.currentLevel,
    required this.approverRole,
    required this.maxLevel,
    required this.remarks,
    required this.approvalDetails,
    required this.detailApproval,
    required this.lastApproverRole,
    required this.detailData,
    required this.lastLevel,
    required this.createdAt,
    required this.updatedAt,
  });

  final int? id;
  final String? requestId;
  final RequestedBy? requestBy;
  final String? trxType;
  final int? trxId;
  final String? approvalStatus;
  final int? currentLevel;
  final String? approverRole;
  final int? maxLevel;
  final String? remarks;
  final List<dynamic> approvalDetails;
  final String? detailApproval;
  final String? lastApproverRole;
  final String? detailData;
  final int? lastLevel;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  factory ApprovalHeader.fromJson(Map<String, dynamic> json) {
    return ApprovalHeader(
      id: json["id"],
      requestId: json["requestId"],
      requestBy:
          json["requestBy"] == null
              ? null
              : RequestedBy.fromJson(json["requestBy"]),
      trxType: json["trxType"],
      trxId: json["trxId"],
      approvalStatus: json["approvalStatus"],
      currentLevel: json["currentLevel"],
      approverRole: json["approverRole"],
      maxLevel: json["maxLevel"],
      remarks: json["remarks"],
      approvalDetails: json["approvalDetails"] ?? [],
      detailApproval: json["detailApproval"],
      lastApproverRole: json["lastApproverRole"],
      detailData: json["detailData"],
      lastLevel: json["lastLevel"],
      createdAt: DateTime.tryParse(json["createdAt"] ?? ""),
      updatedAt: DateTime.tryParse(json["updatedAt"] ?? ""),
    );
  }
}

class RequestedBy {
  RequestedBy({
    required this.username,
    required this.name,
    required this.channel,
    required this.agentCode,
    required this.agentLevel,
    required this.picture,
    required this.roles,
    required this.branches,
  });

  final String? username;
  final String? name;
  final String? channel;
  final String? agentCode;
  final String? agentLevel;
  final String? picture;
  final List<Role> roles;
  final List<Branch> branches;

  factory RequestedBy.fromJson(Map<String, dynamic> json) {
    return RequestedBy(
      username: json["username"],
      name: json["name"],
      channel: json["channel"],
      agentCode: json["agentCode"],
      agentLevel: json["agentLevel"],
      picture: json["picture"],
      roles:
          json["roles"] == null
              ? []
              : List<Role>.from(json["roles"]!.map((x) => Role.fromJson(x))),
      branches:
          json["branches"] == null
              ? []
              : List<Branch>.from(
                json["branches"]!.map((x) => Branch.fromJson(x)),
              ),
    );
  }
}

class Branch {
  Branch({
    required this.id,
    required this.branchCode,
    required this.branchName,
    required this.parentBranchCode,
    required this.parentBranchName,
    required this.phoneNumber,
    required this.staffCount,
    required this.city,
    required this.address,
    required this.secondAddress,
    required this.thirdAddress,
    required this.googleMapsUrl,
    required this.latitude,
    required this.longitude,
    required this.isActive,
    required this.channel,
    required this.areaCode,
    required this.areaName,
    required this.regionCode,
    required this.regionName,
    required this.subRegionCode,
    required this.subRegionName,
    required this.hosCode,
    required this.hosName,
    required this.createdAt,
    required this.updatedAt,
  });

  final int? id;
  final String? branchCode;
  final String? branchName;
  final String? parentBranchCode;
  final String? parentBranchName;
  final String? phoneNumber;
  final int? staffCount;
  final String? city;
  final String? address;
  final String? secondAddress;
  final String? thirdAddress;
  final String? googleMapsUrl;
  final String? latitude;
  final String? longitude;
  final bool? isActive;
  final String? channel;
  final String? areaCode;
  final String? areaName;
  final String? regionCode;
  final String? regionName;
  final String? subRegionCode;
  final String? subRegionName;
  final String? hosCode;
  final String? hosName;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  factory Branch.fromJson(Map<String, dynamic> json) {
    return Branch(
      id: json["id"],
      branchCode: json["branchCode"],
      branchName: json["branchName"],
      parentBranchCode: json["parentBranchCode"],
      parentBranchName: json["parentBranchName"],
      phoneNumber: json["phoneNumber"],
      staffCount: json["staffCount"],
      city: json["city"],
      address: json["address"],
      secondAddress: json["secondAddress"],
      thirdAddress: json["thirdAddress"],
      googleMapsUrl: json["googleMapsUrl"],
      latitude: json["latitude"],
      longitude: json["longitude"],
      isActive: json["isActive"],
      channel: json["channel"],
      areaCode: json["areaCode"],
      areaName: json["areaName"],
      regionCode: json["regionCode"],
      regionName: json["regionName"],
      subRegionCode: json["subRegionCode"],
      subRegionName: json["subRegionName"],
      hosCode: json["hosCode"],
      hosName: json["hosName"],
      createdAt: DateTime.tryParse(json["createdAt"] ?? ""),
      updatedAt: DateTime.tryParse(json["updatedAt"] ?? ""),
    );
  }
}

class Role {
  Role({
    required this.code,
    required this.name,
    required this.channel,
    required this.platform,
  });

  final String? code;
  final String? name;
  final String? channel;
  final String? platform;

  factory Role.fromJson(Map<String, dynamic> json) {
    return Role(
      code: json["code"],
      name: json["name"],
      channel: json["channel"],
      platform: json["platform"],
    );
  }
}

class PolicyTransferDto {
  PolicyTransferDto({
    required this.recipientName,
    required this.recipientAgentCode,
    required this.recipientPicture,
    required this.level,
    required this.sourceAgentCode,
    required this.sourceAgentName,
    required this.sourceAgentPicture,
    required this.sourceAgentLevel,
    required this.branchCode,
    required this.branchName,
    required this.totalPolicies,
    required this.submittedBy,
    required this.submitterAgentCode,
    required this.status,
    required this.requestedAt,
    required this.trxTerminationId,
  });

  final String? recipientName;
  final String? recipientAgentCode;
  final String? recipientPicture;
  final String? level;
  final String? sourceAgentCode;
  final String? sourceAgentName;
  final String? sourceAgentPicture;
  final String? sourceAgentLevel;
  final String? branchCode;
  final String? branchName;
  final int? totalPolicies;
  final String? submittedBy;
  final String? submitterAgentCode;
  final String? status;
  final DateTime? requestedAt;
  final int? trxTerminationId;

  factory PolicyTransferDto.fromJson(Map<String, dynamic> json) {
    return PolicyTransferDto(
      recipientName: json["recipientName"],
      recipientAgentCode: json["recipientAgentCode"],
      recipientPicture: json["recipientPicture"],
      level: json["level"],
      sourceAgentCode: json["sourceAgentCode"],
      sourceAgentName: json["sourceAgentName"],
      sourceAgentPicture: json["sourceAgentPicture"],
      sourceAgentLevel: json["sourceAgentLevel"],
      branchCode: json["branchCode"],
      branchName: json["branchName"],
      totalPolicies: json["totalPolicies"],
      submittedBy: json["submittedBy"],
      submitterAgentCode: json["submitterAgentCode"],
      status: json["status"],
      requestedAt: DateTime.tryParse(json["requestedAt"] ?? ""),
      trxTerminationId: json["trxTerminationId"],
    );
  }
}
