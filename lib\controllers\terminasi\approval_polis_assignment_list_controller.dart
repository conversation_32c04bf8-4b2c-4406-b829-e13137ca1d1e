import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/policy_transfer_model.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ApprovalPolisAssignmentListController extends BaseControllers {
  final TextEditingController reasonTextController = TextEditingController();
  final TextEditingController polisTextController = TextEditingController();
  final RxBool hasChanges = false.obs;
  final RxString agentName = ''.obs;
  final RxString agentCode = ''.obs;
  final RxString submissionDate = ''.obs;
  final RxString level = ''.obs;
  final RxString selectedValue = '0001'.obs;
  late SharedPreferences prefs;

  final Map<String, String> dropdownOptions = {
    '0001': 'BP Dewi Anind<PERSON>',
    '0002': 'BP Dewi <PERSON>tar<PERSON> Put<PERSON>',
    '0003': 'BP Dewi Megumi Suk<PERSON>',
  };

  RxList<PolicyTransferModel> policyTransferList = <PolicyTransferModel>[].obs;

  @override
  void onInit() async {
    super.onInit();

    prefs = await SharedPreferences.getInstance();
    agentCode.value = prefs.getString(kStorageAgentCode) ?? '';

    getPolicyTransferList();
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    switch (requestCode) {
      case kReqPolicyTransferList:
        policyTransferList.clear();
        for (int i = 0; i < response.length; i++) {
          final terminasiData = PolicyTransferModel.fromJson(response[i]);
          policyTransferList.add(terminasiData);
        }
        break;
      default:
    }
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    setLoading(false);
  }

  void getPolicyTransferList() async {
    await api.getPolicyTransferList(
      controllers: this,
      agentCode: agentCode.value,
      code: kReqPolicyTransferList,
    );
  }

  void refreshData() {}
}
