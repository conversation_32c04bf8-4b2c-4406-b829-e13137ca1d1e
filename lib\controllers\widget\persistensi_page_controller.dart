import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/controllers/widget/persistensi_individu_controller.dart';
import 'package:pdl_superapp/controllers/widget/persistensi_team_controller.dart';
import 'package:pdl_superapp/controllers/widget/persistensi_area_controller.dart';
import 'package:pdl_superapp/models/user_models.dart';

import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PersistensiPageController extends BaseControllers {
  final RxBool isExpanded = true.obs;
  final RxInt selectedSection = 0.obs; // 0 for Individu, 1 for Team, 2 for Area
  String level = '';
  String agentCode = '';
  String channel = '';
  bool isShowOtherAgent = false;
  RxBool showFullData = true.obs;

  // Agent profile data for other agent view
  Rx<UserModels?> agentProfile = Rx<UserModels?>(null);
  RxBool isLoadingProfile = false.obs;

  // Filter properties
  RxList<String> selectedBranches = <String>[].obs;

  // Search properties
  RxString searchQuery = ''.obs;
  late TextEditingController searchController;

  // Controllers
  late PersistensiIndividuController individuController = Get.put(
    PersistensiIndividuController(),
    tag: Utils.getRandomString(),
  );
  late PersistensiTeamController teamController = Get.put(
    PersistensiTeamController(),
    tag: Utils.getRandomString(),
  );
  late PersistensiAreaController areaController = Get.put(
    PersistensiAreaController(),
    tag: Utils.getRandomString(),
  );
  late SharedPreferences prefs;

  PersistensiPageController();

  @override
  onInit() async {
    super.onInit();
    prefs = await SharedPreferences.getInstance();
    level = prefs.getString(kStorageUserLevel) ?? '';
    channel = prefs.getString(kStorageUserChannel) ?? '';

    // Initialize search controller
    searchController = TextEditingController();

    // Use agentCode from URL parameter if available, otherwise use stored agentCode
    final urlAgentCode = Get.parameters['agentCode'];
    if (urlAgentCode == null || urlAgentCode.isEmpty) {
      agentCode = prefs.getString(kStorageAgentCode) ?? '';
    }

    // Check if agentCode is provided in URL parameters
    if (urlAgentCode != null && urlAgentCode.isNotEmpty) {
      agentCode = urlAgentCode;
      isShowOtherAgent = true; // Force show other agent data
    }

    // Parse mode from URL parameters for both own and other agent view
    final mode = Get.parameters['mode'];
    if (mode != null && mode.isNotEmpty) {
      final modeIndex = int.tryParse(mode) ?? 0;
      // Validate mode index based on available tabs
      final availableTabsCount = getAvailableTabs().length;
      if (modeIndex >= 0 && modeIndex < availableTabsCount) {
        selectedSection.value = modeIndex;
      }
    } else if (isShowOtherAgent) {
      // Default to team/group view for other agent if no mode specified
      selectedSection.value = 1;
    }

    setLoading(true);

    // Load agent profile if viewing other agent
    if (isShowOtherAgent && agentCode.isNotEmpty) {
      loadAgentProfile();
    }

    refreshData();
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }

  // Toggle accordion expanded state
  void toggleExpanded() {
    isExpanded.value = !isExpanded.value;
  }

  // Switch between sections
  void switchToIndividu() {
    selectedSection.value = 0;
    refreshData();
  }

  void switchToTeam() {
    selectedSection.value = 1;
    refreshData();
  }

  void switchToArea() {
    selectedSection.value = 2;
    refreshData();
  }

  // Load agent profile for other agent view
  Future<void> loadAgentProfile() async {
    if (!isShowOtherAgent || agentCode.isEmpty) return;

    isLoadingProfile.value = true;
    try {
      await api.getSimpleProfile(
        controllers: this,
        agentCode: agentCode,
        code: kReqGetProfile,
      );
    } catch (e) {
      isLoadingProfile.value = false;
    }
  }

  // Refresh data based on current section and user level
  void refreshData() {
    final query = searchQuery.value.trim();
    if (selectedSection.value == 0) {
      individuController.fetchPersistensiData(
        searchQuery: query.isEmpty ? null : query,
      );
    } else if (selectedSection.value == 1) {
      teamController.fetchPersistensiData(
        searchQuery: query.isEmpty ? null : query,
      );
    } else if (selectedSection.value == 2) {
      areaController.fetchPersistensiData(
        branchCodes:
            selectedBranches.isNotEmpty ? selectedBranches.toList() : null,
        searchQuery: query.isEmpty ? null : query,
      );
    }
    setLoading(false);
  }

  // Search functionality
  void performSearch(String query) {
    searchQuery.value = query;
    refreshData();
  }

  // Clear search
  void clearSearch() {
    searchQuery.value = '';
    refreshData();
  }

  // Get available tabs based on channel and role
  List<String> getAvailableTabs() {
    if (channel == kUserChannelBan) {
      // BAN channel
      if (level == "BO") {
        return ['my_self_str'.tr, 'team_str'.tr];
      } else {
        // ASM and above
        return ['my_self_str'.tr, 'team_str'.tr, 'cabang_str'.tr];
      }
    } else {
      // AGE channel - existing logic
      if (level == kLevelBP) return ['my_self_str'.tr];
      return [
        'my_self_str'.tr,
        level == kLevelBD ? 'group_str'.tr : 'team_str'.tr,
      ];
    }
  }

  // Check if should show tabs (for page view)
  bool shouldShowTabs() {
    return getAvailableTabs().length > 1;
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    if (requestCode == kReqGetProfile) {
      if (response != null) {
        agentProfile.value = UserModels.fromJson(response);
      }
      isLoadingProfile.value = false;
    }
  }

  @override
  void loadFailed({required int requestCode, required response}) {
    super.loadFailed(requestCode: requestCode, response: response);

    if (requestCode == kReqGetProfile) {
      isLoadingProfile.value = false;
    }
  }

  // Get page title based on whether viewing other agent
  String getPageTitle() {
    return 'persistency_str'.tr;
  }

  // Apply branch filter only
  void applyBranchFilter(List<String> branches) {
    selectedBranches.assignAll(branches);
    // Apply the filter to the current area controller
    if (selectedSection.value == 2) {
      // For area/cabang view, apply branch filter with multiple codes
      final query = searchQuery.value.trim();
      areaController.fetchPersistensiData(
        branchCodes: branches.isNotEmpty ? branches : null,
        searchQuery: query.isEmpty ? null : query,
      );
    }
  }

  // Reset filter
  void resetFilter() {
    selectedBranches.clear();
    // Reset area controller filter
    areaController.fetchPersistensiData();
    refreshData();
  }
}
