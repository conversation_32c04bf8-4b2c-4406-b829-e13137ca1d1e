import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/termination_candidate_model.dart';
import 'package:pdl_superapp/utils/keys.dart';

class TerminasiTeamController extends BaseControllers {
  RxList<TerminationCandidateModel> terminasiCandidateList =
      <TerminationCandidateModel>[].obs;

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    switch (requestCode) {
      case kReqGetEligibleCandidateTermination:
        terminasiCandidateList.clear();
        var datas = response['content'];
        for (int i = 0; i < datas.length; i++) {
          final terminasiData = TerminationCandidateModel.fromJson(datas[i]);
          terminasiCandidateList.add(terminasiData);
        }
        break;
      default:
    }
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    setLoading(false);
  }

  getTerminasiCandidateList(String agentLevel) async {
    String params = "level=BP";
    switch (agentLevel) {
      case kLevelBP:
        params = "level=BP";
        break;
      case kLevelBM:
        params = "level=BM";
        break;
      case kLevelBD:
        params = "level=BD";
        break;
      default:
        params = "level=BP";
        break;
    }
    await api.getTerminationEligibleCandidates(
      controllers: this,
      params: params,
      code: kReqGetEligibleCandidateTermination,
    );
  }

  void refreshData() {}
}
