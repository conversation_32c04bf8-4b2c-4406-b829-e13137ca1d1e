import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/termination_model.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:pdl_superapp/utils/keys.dart';

class TerminasiDetailController extends BaseControllers {
  final TextEditingController alasanController = TextEditingController();
  final RxBool isSelfTermination = false.obs;
  final RxInt terminationId = 0.obs;
  final RxString terminationStatus = ''.obs;
  final RxString agentName = ''.obs;
  final RxString agentCode = ''.obs;
  final RxString level = ''.obs;
  final RxString reason = ''.obs;
  late SharedPreferences prefs;

  final RxString policyTranferAssignedTo = ''.obs;
  final RxString policyTranferAssignedAgentCode = ''.obs;
  final RxList approvalDetails = [].obs;

  @override
  void onInit() async {
    super.onInit();

    prefs = await SharedPreferences.getInstance();

    if (Get.arguments[kArgsTerminationData] != null) {
      TerminationModel terminationData =
          Get.arguments[kArgsTerminationData] ?? '';
      getTerminationDetail(terminationData.id.toString());
    } else {
      agentCode.value = prefs.getString(kStorageAgentCode) ?? '';
      agentName.value = prefs.getString(kStorageAgentName) ?? '';
      level.value = prefs.getString(kStorageUserLevel) ?? '';

      reason.value = Get.arguments[kArgsTerminationReason] ?? '';
      isSelfTermination(Get.arguments[kArgsSelfTermination] ?? true);
    }
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    switch (requestCode) {
      case kReqGetTerminationDetail:
        final terminationData = TerminationModel.fromJson(response);

        terminationStatus.value = terminationData.status ?? '';
        terminationId.value = terminationData.id ?? 0;
        agentCode.value = terminationData.target?.agentCode ?? '';
        agentName.value = terminationData.target?.name ?? '';
        level.value = terminationData.target?.agentLevel ?? '';

        reason.value = terminationData.reason ?? '';
        var isSelf = Get.arguments[kArgsSelfTermination] ?? true;
        isSelfTermination(isSelf);
        if (!isSelf) {
          policyTranferAssignedTo.value =
              terminationData.policyTransferDto?.recipientName ?? '';
          policyTranferAssignedAgentCode.value =
              terminationData.policyTransferDto?.recipientAgentCode ?? '';
        }
        approvalDetails.value =
            terminationData.approvalHeader?.approvalDetails ?? [];
        break;
      case kReqCancelTermination:
        Get.back();
        Get.back();
        // Get.offNamed(
        //   Routes.TERMINASI_DETAIL,
        //   arguments: {
        //     kArgsTerminationReason: reasonTextController.text,
        //     kArgsSelfTermination: true,
        //   },
        // );
        break;
      default:
        break;
    }
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    setLoading(false);
  }

  getTerminationDetail(String id) async {
    await api.getTerminationDetail(
      controllers: this,
      params: id,
      code: kReqGetTerminationDetail,
    );
  }

  cancelTerminasi() async {
    var params = {"id": terminationId.value, "remarks": alasanController.text};

    setLoading(true);
    await api.cancelTermination(
      controllers: this,
      data: params,
      code: kReqCancelTermination,
    );
  }

  void refreshData() {}
}
