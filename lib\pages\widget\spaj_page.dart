import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:get/get.dart';
import 'package:get/get_rx/src/rx_workers/utils/debouncer.dart';
import 'package:intl/intl.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/filter_button.dart';
import 'package:pdl_superapp/components/home/<USER>';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/components/pdl_text_field.dart';
import 'package:pdl_superapp/controllers/widget/spaj_controller.dart';
import 'package:pdl_superapp/models/widget/widget_spaj_models.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/extensions/string_ext.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';

import '../../components/custom_chip.dart';
import '../../components/pdl_bottom_sheet.dart';

class SpajPage extends StatefulWidget {
  const SpajPage({super.key});

  @override
  State<SpajPage> createState() => _SpajPageState();
}

class _SpajPageState extends State<SpajPage> {
  late ScrollController scrollController;
  final debouncer = Debouncer(delay: const Duration(milliseconds: 700));

  final SpajController controller = Get.put(
    SpajController(idxSection: Get.parameters['tab']),
  );

  void _loadMore() {
    if (scrollController.position.pixels >=
            (scrollController.position.maxScrollExtent - 100) &&
        !controller.isLoading.value) {
      if (controller.currentPage.value < controller.totalPages.value) {
        controller.currentPage.value += 1;
        controller.load();
      }
    }
  }

  @override
  void initState() {
    scrollController = ScrollController()..addListener(_loadMore);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BaseDetailPage(
      onRefresh: () => controller.refreshData(),
      backEnabled: true,
      controller: controller,
      scrollController: scrollController,
      title: 'status_spaj_str'.tr,
      child: Container(
        width: Get.width,
        padding: const EdgeInsets.symmetric(horizontal: paddingMedium),
        child: Obx(() {
          if (controller.hasError.isTrue) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, size: 48, color: Colors.red),
                  const SizedBox(height: 8),
                  Text('Error: ${controller.errorMessage.value}'),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => controller.load(),
                    child: Text('retry_str'.tr),
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              const SizedBox(height: paddingMedium),
              // Search and Filter
              SizedBox(
                width: Get.width,
                child: Row(
                  children: [
                    Expanded(
                      child: PdlTextField(
                        hint: 'spaj_search_desc_str'.tr,
                        textController: controller.searchController,
                        onChanged: (value) => controller.onSearch(value),
                        prefixIcon: Padding(
                          padding: EdgeInsets.all(paddingMedium),
                          child: Utils.cachedSvgWrapper(
                            'icon/ic-linear-search -2.svg',
                            color: Get.isDarkMode ? kColorTextDark : null,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: paddingMedium),
                    FilterButton(
                      dismissable: true,
                      notificationCount: controller.filterValue.length,
                      content: _filterContent(context),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: paddingMedium),
              if (controller.isLoading.isTrue &&
                  controller.currentPage.value == 0)
                Center(child: CircularProgressIndicator()),

              _buildContent(context),
            ],
          );
        }),
      ),
    );
  }

  // Content based on role and selected tab
  Widget _buildContent(BuildContext context) {
    if (controller.arrData.isEmpty && !controller.isLoading.value) {
      return Column(
        children: [
          Utils.cachedSvgWrapper(
            'icon/ic-no-results.svg',
            width: Get.width / 2,
          ),
          Center(
            child: Padding(
              padding: EdgeInsets.all(paddingMedium),
              child: Text("no_data_message".tr),
            ),
          ),
          WidgetLoadMore(onTap: () => controller.load()),
        ],
      );
    }

    return Column(
      children: [
        for (int i = 0; i < controller.arrData.length; i++)
          _card(
            context,
            spajData: controller.arrData[i],
            isLast: i == controller.arrData.length - 1,
          ),
        if (controller.isLoading.value && controller.currentPage.value > 0)
          CircularProgressIndicator(),
        const SizedBox(height: paddingMedium),
      ],
    );
  }

  SizedBox _filterContent(BuildContext context) {
    return SizedBox(
      width: Get.width,
      child: Column(
        spacing: paddingSmall,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (controller.userLevel.inList([kLevelBM, kLevelASM]))
            Text(
              "view_str".tr,
              style: Theme.of(
                context,
              ).textTheme.bodyLarge!.copyWith(fontWeight: FontWeight.bold),
            ),
          if (controller.userLevel.inList([kLevelBM, kLevelASM]))
            RowView(controller: controller),
          Text(
            "status_str".tr,
            style: Theme.of(
              context,
            ).textTheme.bodyLarge!.copyWith(fontWeight: FontWeight.bold),
          ),
          ...kSpajStatus.asMap().entries.map(
            (e) => _filterCard(context, spajStatus: e.value, index: e.key),
          ),
          SizedBox(height: paddingMedium),
          Padding(
            padding: EdgeInsets.only(bottom: paddingMedium),
            child: Row(
              children: [
                Expanded(
                  child: PdlButton(
                    title: "reset_str".tr,
                    borderColor: Colors.transparent,
                    backgroundColor: Colors.transparent,
                    foregorundColor:
                        Get.isDarkMode ? kColorTextDark : kColorTextLight,
                    onPressed: () => controller.onTapClearFilter(),
                  ),
                ),
                SizedBox(width: paddingMedium),
                Expanded(
                  child: PdlButton(
                    title: "apply_str".tr,
                    onPressed: () => controller.onTapTerapkan(),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _filterCard(
    BuildContext context, {
    required String spajStatus,
    required int index,
  }) {
    return GestureDetector(
      onTap: () => controller.onTapFilter(spajStatus),
      child: Container(
        padding: EdgeInsets.only(bottom: paddingSmall),
        color: Colors.transparent,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (index != 0) Divider(),
            Row(
              children: [
                Obx(() {
                  if (controller.filterValue.contains(spajStatus)) {
                    return Icon(Icons.check_box, color: kColorGlobalBlue);
                  } else {
                    return Icon(
                      Icons.check_box_outline_blank,
                      color: Color(0xFFD1D1D1),
                    );
                  }
                }),
                SizedBox(width: 4),
                Expanded(
                  child: Text(
                    "${spajStatus}_str".tr,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _card(context, {required WidgetSpajModels spajData, bool? isLast}) {
    return Container(
      padding: EdgeInsets.only(top: paddingSmall),
      width: Get.width,
      child: Column(
        children: [
          if (controller.userLevel != kLevelBP)
            Padding(
              padding: EdgeInsets.only(bottom: paddingSmall),
              child: Row(
                children: [
                  CircleAvatar(
                    child: Center(child: Text(spajData.agentName?[0] ?? '-')),
                  ),
                  SizedBox(width: paddingSmall),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          spajData.agentName == null
                              ? '-'
                              : "${spajData.agentName}${(controller.userLevel.inList([kLevelBM, kLevelBD]) && controller.agentName == spajData.agentName && controller.selectedParam == 1) ? " (${"me_str".tr})" : ""}",
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(fontWeight: FontWeight.w700),
                        ),
                        Text(
                          spajData.agentCode ?? '-',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          _cardTitle(context, spajData: spajData),
          SizedBox(height: paddingSmall),

          _cardContent(context, spajData: spajData),
          _notesContent(
            context,
            value: spajData.descriptionPending ?? "",
            desc: "status_str".tr,
          ),
          Align(
            alignment: Alignment.centerRight,
            child: TextButton(
              style: ButtonStyle(
                padding: WidgetStatePropertyAll(EdgeInsets.zero),
              ),
              onPressed:
                  () => showNotesSpajBottomSheet(
                    context,
                    spajData.remarkPending ?? "",
                  ),
              child: Text(
                "see_details_str".tr,
                style: TextStyle(color: Colors.blue.shade400, fontSize: 15),
              ),
            ),
          ),
          if (isLast != true) Divider(),
        ],
      ),
    );
  }

  Padding _cardContent(context, {required WidgetSpajModels spajData}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: paddingSmall),
      child: Column(
        children: [
          _contentField(
            context,
            title: 'policy_holder_name_str'.tr,
            value: spajData.policyHolderName ?? '-',
          ),
          _contentField(
            context,
            title: 'premium_str'.tr,
            value: Utils.currencyFormatters(
              data: spajData.basicPremium.toString(),
              currency: spajData.currency,
            ),
          ),
          _contentField(
            context,
            title: 'payment_period_str'.tr,
            value: spajData.frequency ?? '-',
          ),
          if (spajData.spajStatus != kSpajStatAccept)
            _contentField(
              context,
              title: 'submit_date_str'.tr,
              value: DateFormat(
                'dd/MM/yyyy',
              ).format(DateTime.parse(spajData.submitDate!)),
            ),
        ],
      ),
    );
  }

  Row _contentField(context, {required String title, required String value}) {
    return Row(
      children: [
        Expanded(child: Text(title)),
        Text(
          value,
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700),
        ),
      ],
    );
  }

  Row _cardTitle(context, {required WidgetSpajModels spajData}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
          child: Text(
            spajData.spajNumber ?? '-',
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w700),
          ),
        ),
        CustomChip(
          type:
              [
                    kSpajStatAccept,
                    kSpajStatAcceptPendingPrint,
                    kSpajStatPolicy,
                  ].contains(spajData.spajStatus)
                  ? ChipType.success
                  : ChipType.warning,
          text:
              spajData.spajStatus != null
                  ? "${spajData.spajStatus!.toLowerCase().replaceAll(" ", "_")}_status_str"
                      .tr
                  : "-",
        ),
      ],
    );
  }

  Widget _notesContent(context, {required String value, required String desc}) {
    return SizedBox(
      width: Get.width,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            desc.tr,
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w700),
          ),
          Html(
            data: value.isNotEmpty ? value : "-",
            style: {"body": Style(margin: Margins.all(0))},
          ),
        ],
      ),
    );
  }
}

class FilterViewButton extends StatefulWidget {
  final int index;
  final int selectedIndex;
  final String label;
  final Function() onTap;

  const FilterViewButton({
    super.key,
    required this.index,
    required this.selectedIndex,
    required this.label,
    required this.onTap,
  });

  @override
  State<FilterViewButton> createState() => _FilterViewButtonState();
}

class _FilterViewButtonState extends State<FilterViewButton> {
  @override
  Widget build(BuildContext context) {
    bool isSelected = widget.index == widget.selectedIndex;
    return InkWell(
      onTap: () {
        widget.onTap.call();
      },
      child: Container(
        width: 100,
        padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 5),
        decoration: BoxDecoration(
          color:
              isSelected
                  ? Colors.white.withValues(alpha: 0.5)
                  : Colors.transparent,
          borderRadius: BorderRadius.circular(8.0),
          border: Border.all(
            color: isSelected ? Colors.grey.shade400 : Colors.grey.shade300,
            width: 1.0,
          ),
        ),
        child: Center(
          child: Text(
            widget.label.tr,
            style: TextStyle(
              fontSize: 16.0,
              fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
              color: isSelected ? Colors.black87 : Colors.grey.shade700,
            ),
          ),
        ),
      ),
    );
  }
}

class RowView extends StatefulWidget {
  final SpajController controller;

  const RowView({super.key, required this.controller});

  @override
  State<RowView> createState() => _RowViewState();
}

class _RowViewState extends State<RowView> {
  late int selectedIndex;

  @override
  void initState() {
    super.initState();
    selectedIndex = widget.controller.selectedParam;
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        FilterViewButton(
          index: 0,
          label: "individu_str",
          onTap: () {
            setState(() {
              selectedIndex = 0;
              widget.controller.selectedParam = selectedIndex;
            });
          },
          selectedIndex: selectedIndex,
        ),
        SizedBox(width: 10),
        FilterViewButton(
          index: 1,
          label: "team_str",
          onTap: () {
            setState(() {
              selectedIndex = 1;
              widget.controller.selectedParam = selectedIndex;
            });
          },
          selectedIndex: selectedIndex,
        ),
      ],
    );
  }
}

void showNotesSpajBottomSheet(context, String value) {
  PdlBottomSheet(
    title: "notes_str".tr,
    content: SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: paddingMedium),
          Padding(
            padding: const EdgeInsets.only(bottom: paddingMedium),
            child: Html(
              data: value.isNotEmpty ? value : "-",
              style: {"body": Style(margin: Margins.all(0))},
            ),
          ),
          const SizedBox(height: paddingMedium),
        ],
      ),
    ),
  );
}
