import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path/path.dart';
import 'package:pdl_superapp/services/pending_photo_service.dart';
import 'package:pdl_superapp/utils/analytics_utils.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class ImageForm extends StatelessWidget {
  const ImageForm({
    required this.title,
    required this.imageFile,
    required this.onTapAdd,
    this.imageUrl,
    this.description,
    this.uploadText,
    this.isUploading,
    this.uploadProgress,
    this.onClear,
    this.errorText,
    this.hasError = false,
    this.isDisabled,
    this.photoType,
    super.key,
  });

  final String title;
  final Rx<dynamic> imageFile; // Can be File or XFile
  final Function() onTapAdd;
  final String? description;
  final RxString? imageUrl;
  final String? uploadText;
  final RxBool? isUploading;
  final RxDouble? uploadProgress;
  final Function()? onClear;
  final String? errorText;
  final bool hasError;
  final bool? isDisabled;
  final String? photoType; // Type of photo for pending status check

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: Get.width,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700),
          ),
          SizedBox(height: paddingSmall),
          Obx(() {
            if (imageFile.value != null ||
                (imageUrl?.value.isNotEmpty == true)) {
              // Display the KTP image if available
              return Container(
                padding: EdgeInsets.all(paddingMedium),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(radiusMedium),
                  border: Border.all(
                    color:
                        hasError
                            ? Colors.red
                            : (Get.isDarkMode
                                ? kColorBorderDark
                                : kColorBorderLight),
                  ),
                ),
                width: Get.width,
                child: Row(
                  children: [
                    Container(
                      width: 70,
                      height: 70,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(radiusSmall),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(radiusSmall - 2),
                        child:
                            imageFile.value != null
                                ? _buildImageWidget(imageFile.value!)
                                : Utils.cachedImageWrapper(
                                  imageUrl?.value,
                                  isFullUrl: true,
                                  fit: BoxFit.cover,
                                ),
                      ),
                    ),
                    SizedBox(width: paddingSmall),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  _getFileName(imageFile.value),
                                  style: Theme.of(context).textTheme.bodyMedium
                                      ?.copyWith(fontWeight: FontWeight.w500),
                                ),
                              ),
                              // Local photo indicator
                              if (_isPhotoLocal())
                                Container(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: paddingSmall / 2,
                                    vertical: 2,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.orange.withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(4),
                                    border: Border.all(
                                      color: Colors.orange,
                                      width: 1,
                                    ),
                                  ),
                                  child: Text(
                                    'Local',
                                    style: Theme.of(
                                      context,
                                    ).textTheme.bodySmall?.copyWith(
                                      color: Colors.orange,
                                      fontWeight: FontWeight.w600,
                                      fontSize: 10,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                          if (!kIsWeb)
                            if (imageFile.value != null)
                              Text(
                                _getFileSize(imageFile.value!),
                                style: Theme.of(context).textTheme.bodySmall
                                    ?.copyWith(fontWeight: FontWeight.w500),
                              ),
                          // Local photo warning
                          if (_isPhotoLocal())
                            Padding(
                              padding: EdgeInsets.only(top: paddingSmall / 2),
                              child: Text(
                                'Foto belum terupload ke server',
                                style: Theme.of(
                                  context,
                                ).textTheme.bodySmall?.copyWith(
                                  color: Colors.orange,
                                  fontWeight: FontWeight.w500,
                                  fontSize: 11,
                                ),
                              ),
                            ),
                          // Upload progress indicator
                          if (isUploading?.value == true)
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                SizedBox(height: paddingSmall),
                                Text(
                                  'Uploading... ${(uploadProgress?.value ?? 0).toInt()}%',
                                  style: Theme.of(
                                    context,
                                  ).textTheme.bodySmall?.copyWith(
                                    color: kColorGlobalBlue,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                SizedBox(height: paddingSmall / 2),
                                LinearProgressIndicator(
                                  value: uploadProgress?.value ?? 0,
                                  backgroundColor: Colors.grey[300],
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    kColorGlobalBlue,
                                  ),
                                ),
                              ],
                            ),
                          // GestureDetector(
                          //   onTap:
                          //       () => controller.textCognition(
                          //         File(imageFile.value!.path),
                          //       ),
                          //   child: Text(
                          //     'get data ocr (test only)',
                          //     style: Theme.of(
                          //       context,
                          //     ).textTheme.bodyMedium?.copyWith(
                          //       fontWeight: FontWeight.w700,
                          //       color: kColorGlobalBlue,
                          //       decoration: TextDecoration.underline,
                          //       decorationThickness: 2,
                          //       decorationColor: kColorGlobalBlue,
                          //       decorationStyle: TextDecorationStyle.solid,
                          //     ),
                          //   ),
                          // ),
                        ],
                      ),
                    ),
                    if (isDisabled != true)
                      GestureDetector(
                        onTap: () {
                          if (onClear != null) {
                            onClear!();
                          }
                        },
                        child: Container(
                          color: Colors.transparent,
                          child: Utils.cachedSvgWrapper(
                            'icon/ic-linear-trash-bin.svg',
                            width: 30,
                            color: kColorGlobalRed,
                          ),
                        ),
                      ),
                  ],
                ),
              );
            } else {
              // Show the upload/take photo button if no image
              return GestureDetector(
                onTap: () {
                  // Track image upload action
                  AnalyticsUtils.trackElementClick(
                    elementName: 'Upload Photo Button Clicked',
                    elementType: 'Upload Button',
                    section: 'Form Input',
                    action: 'tap',
                    additionalData: {'action_type': 'image_upload'},
                  );
                  onTapAdd();
                },
                child: Utils.customDottedBorder(
                  child: Container(
                    width: Get.width,
                    color: Colors.transparent,
                    padding: EdgeInsets.all(paddingMedium),
                    child: Column(
                      children: [
                        Utils.cachedSvgWrapper(
                          'icon/ic-linear-image.svg',
                          color: kColorGlobalBlue,
                          width: 40,
                        ),
                        FittedBox(
                          fit: BoxFit.fill,
                          child: Text(
                            uploadText ?? 'Unggah atau ambil foto',
                            maxLines: 1,
                            style: Theme.of(
                              context,
                            ).textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w700,
                              color: kColorGlobalBlue,
                              decoration: TextDecoration.underline,
                              decorationThickness: 2,
                              decorationColor: kColorGlobalBlue,
                              decorationStyle: TextDecorationStyle.solid,
                              height: 2,
                            ),
                          ),
                        ),
                        if (description != null)
                          Text(
                            description!,
                            textAlign: TextAlign.center,
                            style: Theme.of(
                              context,
                            ).textTheme.bodyMedium?.copyWith(
                              height: 2,
                              color:
                                  Get.isDarkMode
                                      ? kColorTextTersierLight
                                      : kColorTextTersier,
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              );
            }
          }),
          SizedBox(height: paddingSmall),
          if (errorText != null && errorText!.isNotEmpty)
            Padding(
              padding: EdgeInsets.only(bottom: paddingSmall),
              child: Text(
                errorText!,
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: Colors.red),
              ),
            ),
          if (isDisabled != true)
            Text(
              'Maks. 2MB dengan format JPEG/PNG',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color:
                    Get.isDarkMode ? kColorTextTersierLight : kColorTextTersier,
              ),
            ),
        ],
      ),
    );
  }

  /// Build image widget that handles both File and XFile
  Widget _buildImageWidget(dynamic imageFile) {
    if (kIsWeb) {
      // On web, handle XFile
      if (imageFile is XFile) {
        return FutureBuilder<Uint8List>(
          future: imageFile.readAsBytes(),
          builder: (context, snapshot) {
            if (snapshot.hasData) {
              return Image.memory(snapshot.data!, fit: BoxFit.cover);
            }
            return const Center(child: CircularProgressIndicator());
          },
        );
      } else {
        // Fallback for web
        return const Center(child: Icon(Icons.image, size: 50));
      }
    } else {
      // On mobile, handle File
      try {
        return Image.file(imageFile, fit: BoxFit.cover);
      } catch (e) {
        return const Center(child: Icon(Icons.image, size: 50));
      }
    }
  }

  /// Get file name from either File or XFile
  String _getFileName(dynamic imageFile) {
    if (imageFile == null) return 'Image.jpg';

    if (imageFile is XFile) {
      try {
        return imageFile.name == '' ? 'Image.jpg' : imageFile.name;
      } catch (e) {
        return 'Image.jpg';
      }
    } else {
      try {
        return basename(imageFile.path);
      } catch (e) {
        return 'Image.jpg';
      }
    }
  }

  /// Get file size (mobile only)
  String _getFileSize(dynamic imageFile) {
    if (kIsWeb) return '';

    try {
      if (imageFile?.path != null) {
        return Utils.getFileSize(imageFile.path, 1);
      }
    } catch (e) {
      // Ignore error
    }
    return '';
  }

  /// Check if photo is only available locally (not uploaded to server)
  bool _isPhotoLocal() {
    // Photo is considered local if:
    // 1. There's a local image file but no server URL
    // 2. Or if we have a photo type and it's in pending photos list

    final hasLocalImage = imageFile.value != null;
    final hasServerUrl = imageUrl?.value.isNotEmpty == true;

    if (!hasLocalImage) return false;

    // If no server URL, it's definitely local
    if (!hasServerUrl) return true;

    // Additional check: if we have photoType, check pending photos service
    if (photoType != null && !kIsWeb) {
      try {
        final pendingService = Get.find<PendingPhotoService>();
        final imagePath = imageFile.value?.path ?? '';
        return pendingService.isPhotoPending(imagePath, photoType!);
      } catch (e) {
        // Service not available, fallback to URL check
        return !hasServerUrl;
      }
    }

    return false;
  }
}
