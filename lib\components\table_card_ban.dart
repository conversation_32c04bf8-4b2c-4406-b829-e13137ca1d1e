import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:pdl_superapp/components/pdl_circle_avatar.dart';
import 'package:pdl_superapp/components/pdl_circle_avatar_initial.dart';
import 'package:pdl_superapp/models/validasi_ban_model.dart';
import '../utils/constants.dart';

class TableCardBan extends StatelessWidget {
  final bool isIndividu;
  final String title;
  final ValidasiBanTeam data;
  final Map<int, FlexColumnWidth>? columnWidths;
  final Map<int, FlexColumnWidth>? headerColumnWidths;
  final List<int>? boldRows; // Indices of rows that should be bold
  final Map<int, double>? minColumnWidths; // Minimum width for each column
  final bool
  horizontalScrollable; // Whether the table should be horizontally scrollable

  const TableCardBan({
    super.key,
    this.isIndividu = false,
    this.title = '',
    required this.data,
    this.columnWidths,
    this.headerColumnWidths,
    this.boldRows,
    this.minColumnWidths,
    this.horizontalScrollable = false,
  });

  // Generate column widths based on the provided parameter or use default values
  Map<int, TableColumnWidth> _getColumnWidths() {
    // Default column widths (equal distribution)
    Map<int, TableColumnWidth> defaultWidths = {};

    // Create default widths based on the number of headers
    for (int i = 0; i < data.result.length; i++) {
      if (minColumnWidths != null && minColumnWidths!.containsKey(i)) {
        // Use FixedColumnWidth with min width constraint if specified
        double minWidth =
            minColumnWidths![i] ?? 100.0; // Default to 100.0 if null
        defaultWidths[i] = FixedColumnWidth(minWidth);
      } else {
        defaultWidths[i] = const FlexColumnWidth(1);
      }
    }

    // Apply custom column widths if provided
    if (columnWidths != null) {
      columnWidths!.forEach((key, value) {
        // If min width is specified, use the larger of the two
        if (minColumnWidths != null && minColumnWidths!.containsKey(key)) {
          double minWidth =
              minColumnWidths![key] ?? 100.0; // Default to 100.0 if null
          defaultWidths[key] = FixedColumnWidth(minWidth);
        } else {
          defaultWidths[key] = value;
        }
      });
    }

    return defaultWidths;
  }

  // Generate header column widths based on the provided parameter or use default values
  Map<int, TableColumnWidth> _getHeaderColumnWidths() {
    // Start with the regular column widths
    Map<int, TableColumnWidth> headerWidths = Map.from(_getColumnWidths());

    // Apply header-specific widths if provided
    if (headerColumnWidths != null) {
      headerColumnWidths!.forEach((key, value) {
        // If min width is specified, use the larger of the two
        if (minColumnWidths != null && minColumnWidths!.containsKey(key)) {
          double minWidth =
              minColumnWidths![key] ?? 100.0; // Default to 100.0 if null
          headerWidths[key] = FixedColumnWidth(minWidth);
        } else {
          headerWidths[key] = value;
        }
      });
    }

    return headerWidths;
  }

  @override
  Widget build(BuildContext context) {
    final DateFormat formatter = DateFormat('MMM');

    // Create the table content
    Widget tableContent = Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(radiusSmall),
        border: Border.all(
          width: 0.5,
          color: Get.isDarkMode ? Colors.grey : Colors.grey.shade300,
        ),
      ),
      child: Column(
        children: [
          // Header row with potentially different column widths
          Table(
            columnWidths: _getHeaderColumnWidths(),
            children: [
              TableRow(
                decoration: BoxDecoration(
                  color: Color(0xFF0075BD),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(radiusSmall),
                    topRight: Radius.circular(radiusSmall),
                  ),
                ),
                children:
                    data.result
                        .map(
                          (row) => Padding(
                            padding: const EdgeInsets.all(paddingSmall),
                            child: Text(
                              formatter.format(DateTime.parse(row.periode)),
                              textAlign: TextAlign.center,
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        )
                        .toList(),
              ),
            ],
          ),

          Table(
            columnWidths: _getColumnWidths(),
            children: [
              TableRow(
                children:
                    data.result
                        .map(
                          (row) => Padding(
                            padding: const EdgeInsets.all(paddingSmall),
                            child: Text(
                              row.abu,
                              textAlign: TextAlign.center,
                              style: const TextStyle(
                                fontWeight: FontWeight.normal,
                              ),
                            ),
                          ),
                        )
                        .toList(),
              ),
            ],
          ),
        ],
      ),
    );

    // Wrap in horizontal scroll view if needed
    if (horizontalScrollable) {
      tableContent = SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: tableContent,
      );
    }

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (!isIndividu) ...[
          PdlCircleAvatar(
            source: data.agentPhoto,
            border: true,
            height: 45,
            width: 45,
            errorWidget: PdlCircleAvatarInitial(name: data.agentName),
          ),
          SizedBox(width: paddingSmall),
        ],
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                (isIndividu ? title : '${data.agentLevel} ${data.agentName}'),
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: paddingSmall),
              tableContent,
            ],
          ),
        ),
      ],
    );
  }
}
