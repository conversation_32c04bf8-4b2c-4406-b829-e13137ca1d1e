import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/services/background_upload_service.dart';
import 'package:pdl_superapp/services/pending_photo_service.dart';
import 'package:pdl_superapp/utils/constants.dart';

class DebugUploadWidget extends StatelessWidget {
  const DebugUploadWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.all(paddingMedium),
      padding: EdgeInsets.all(paddingMedium),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(radiusMedium),
        border: Border.all(color: Colors.orange, width: 2),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.bug_report, color: Colors.orange, size: 20),
              SizedBox(width: paddingSmall),
              Text(
                'Background Upload Debug',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ],
          ),
          SizedBox(height: paddingMedium),
          _buildServiceStatus(),
          SizedBox(height: paddingMedium),
          _buildPendingPhotosInfo(),
          SizedBox(height: paddingMedium),
          _buildControlButtons(),
        ],
      ),
    );
  }

  Widget _buildServiceStatus() {
    try {
      final service = Get.find<BackgroundUploadService>();
      return Obx(
        () => Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildStatusRow(
              'Service Active',
              service.isBackgroundUploadActive ? 'Yes' : 'No',
              service.isBackgroundUploadActive ? Colors.green : Colors.red,
            ),
            _buildStatusRow(
              'Debug Mode',
              service.debugMode.value ? 'Enabled' : 'Disabled',
              service.debugMode.value ? Colors.green : Colors.grey,
            ),
            _buildStatusRow(
              'Currently Uploading',
              service.isUploading.value ? 'Yes' : 'No',
              service.isUploading.value ? Colors.orange : Colors.grey,
            ),
            _buildStatusRow(
              'Total Pending',
              '${service.totalPendingPhotos.value}',
              service.totalPendingPhotos.value > 0
                  ? Colors.orange
                  : Colors.green,
            ),
            _buildStatusRow(
              'Uploaded Count',
              '${service.uploadedPhotosCount.value}',
              Colors.blue,
            ),
          ],
        ),
      );
    } catch (e) {
      return Text(
        'BackgroundUploadService not found',
        style: TextStyle(color: Colors.red, fontSize: 12),
      );
    }
  }

  Widget _buildPendingPhotosInfo() {
    try {
      final service = Get.find<PendingPhotoService>();
      return Obx(() {
        final pendingPhotos = service.pendingPhotos;
        if (pendingPhotos.isEmpty) {
          return Text(
            'No pending photos',
            style: TextStyle(color: Colors.green, fontSize: 12),
          );
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Pending Photos:',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
            SizedBox(height: paddingSmall),
            ...pendingPhotos
                .take(3)
                .map(
                  (photo) => Padding(
                    padding: EdgeInsets.only(bottom: 4),
                    child: Text(
                      '• ${photo.type} (${photo.uploadAttempts} attempts)',
                      style: TextStyle(color: Colors.orange, fontSize: 11),
                    ),
                  ),
                ),
            if (pendingPhotos.length > 3)
              Text(
                '... and ${pendingPhotos.length - 3} more',
                style: TextStyle(color: Colors.grey, fontSize: 11),
              ),
          ],
        );
      });
    } catch (e) {
      return Text(
        'PendingPhotoService not found',
        style: TextStyle(color: Colors.red, fontSize: 12),
      );
    }
  }

  Widget _buildControlButtons() {
    try {
      final service = Get.find<BackgroundUploadService>();
      return Wrap(
        spacing: paddingSmall,
        runSpacing: paddingSmall,
        children: [
          _buildDebugButton(
            'Toggle Debug',
            () => service.toggleDebugMode(),
            Colors.orange,
          ),
          _buildDebugButton(
            'Force Upload',
            () => service.uploadAllPendingPhotos(),
            Colors.blue,
          ),
          _buildDebugButton(
            'Pause Service',
            () => service.pauseBackgroundUpload(),
            Colors.red,
          ),
          _buildDebugButton(
            'Resume Service',
            () => service.resumeBackgroundUpload(),
            Colors.green,
          ),
        ],
      );
    } catch (e) {
      return Text(
        'BackgroundUploadService not found',
        style: TextStyle(color: Colors.red, fontSize: 12),
      );
    }
  }

  Widget _buildStatusRow(String label, String value, Color color) {
    return Padding(
      padding: EdgeInsets.only(bottom: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: TextStyle(color: Colors.white, fontSize: 12)),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDebugButton(String text, VoidCallback onPressed, Color color) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: EdgeInsets.symmetric(
          horizontal: paddingSmall,
          vertical: paddingSmall / 2,
        ),
        minimumSize: Size(0, 32),
      ),
      child: Text(text, style: TextStyle(fontSize: 11)),
    );
  }
}

/// Extension to easily add debug widget to any page
extension DebugUploadExtension on Widget {
  Widget withDebugUpload({bool show = true}) {
    if (!show) return this;

    return Stack(
      children: [
        this,
        Positioned(top: 100, right: 16, child: DebugUploadWidget()),
      ],
    );
  }
}
