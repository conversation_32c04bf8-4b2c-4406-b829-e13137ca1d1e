import 'dart:developer';
import 'package:get/get.dart';
import 'package:pdl_superapp/services/pending_photo_service.dart';
import 'package:pdl_superapp/services/background_upload_service.dart';

class ServiceInitializer {
  /// Initialize all required services for the app
  static Future<void> initializeServices() async {
    try {
      log('Initializing app services...');
      
      // Initialize PendingPhotoService
      if (!Get.isRegistered<PendingPhotoService>()) {
        final pendingPhotoService = Get.put(PendingPhotoService(), permanent: true);
        await pendingPhotoService.onInit();
        log('PendingPhotoService initialized');
      }
      
      // Initialize BackgroundUploadService
      if (!Get.isRegistered<BackgroundUploadService>()) {
        final backgroundUploadService = Get.put(BackgroundUploadService(), permanent: true);
        await backgroundUploadService.onInit();
        log('BackgroundUploadService initialized');
      }
      
      log('All services initialized successfully');
    } catch (e) {
      log('Error initializing services: $e');
    }
  }

  /// Cleanup services when app is closing
  static Future<void> cleanupServices() async {
    try {
      log('Cleaning up app services...');
      
      // Cleanup BackgroundUploadService
      if (Get.isRegistered<BackgroundUploadService>()) {
        final backgroundUploadService = Get.find<BackgroundUploadService>();
        backgroundUploadService.onClose();
        Get.delete<BackgroundUploadService>();
        log('BackgroundUploadService cleaned up');
      }
      
      // Cleanup PendingPhotoService
      if (Get.isRegistered<PendingPhotoService>()) {
        Get.delete<PendingPhotoService>();
        log('PendingPhotoService cleaned up');
      }
      
      log('All services cleaned up successfully');
    } catch (e) {
      log('Error cleaning up services: $e');
    }
  }

  /// Check if all required services are initialized
  static bool areServicesInitialized() {
    return Get.isRegistered<PendingPhotoService>() && 
           Get.isRegistered<BackgroundUploadService>();
  }

  /// Get service status for debugging
  static Map<String, bool> getServiceStatus() {
    return {
      'PendingPhotoService': Get.isRegistered<PendingPhotoService>(),
      'BackgroundUploadService': Get.isRegistered<BackgroundUploadService>(),
    };
  }
}
