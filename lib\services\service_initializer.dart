import 'dart:developer';
import 'package:get/get.dart';
import 'package:pdl_superapp/services/pending_photo_service.dart';
import 'package:pdl_superapp/services/background_upload_service.dart';

class ServiceInitializer {
  /// Initialize all required services for the app
  static Future<void> initializeServices() async {
    try {
      print('DEBUG: Starting service initialization...');
      log('Initializing app services...');

      // Initialize PendingPhotoService
      if (!Get.isRegistered<PendingPhotoService>()) {
        print('DEBUG: Initializing PendingPhotoService...');
        final pendingPhotoService = Get.put(
          PendingPhotoService(),
          permanent: true,
        );
        await pendingPhotoService.onInit();
        print('DEBUG: PendingPhotoService initialized successfully');
        log('PendingPhotoService initialized');
      } else {
        print('DEBUG: PendingPhotoService already registered');
      }

      // Initialize BackgroundUploadService
      if (!Get.isRegistered<BackgroundUploadService>()) {
        print('DEBUG: Initializing BackgroundUploadService...');
        final backgroundUploadService = Get.put(
          BackgroundUploadService(),
          permanent: true,
        );
        await backgroundUploadService.onInit();
        print('DEBUG: BackgroundUploadService initialized successfully');
        log('BackgroundUploadService initialized');
      } else {
        print('DEBUG: BackgroundUploadService already registered');
      }

      print('DEBUG: All services initialization completed');
      log('All services initialized successfully');
    } catch (e) {
      print('DEBUG: Error initializing services: $e');
      log('Error initializing services: $e');
    }
  }

  /// Cleanup services when app is closing
  static Future<void> cleanupServices() async {
    try {
      log('Cleaning up app services...');

      // Cleanup BackgroundUploadService
      if (Get.isRegistered<BackgroundUploadService>()) {
        final backgroundUploadService = Get.find<BackgroundUploadService>();
        backgroundUploadService.onClose();
        Get.delete<BackgroundUploadService>();
        log('BackgroundUploadService cleaned up');
      }

      // Cleanup PendingPhotoService
      if (Get.isRegistered<PendingPhotoService>()) {
        Get.delete<PendingPhotoService>();
        log('PendingPhotoService cleaned up');
      }

      log('All services cleaned up successfully');
    } catch (e) {
      log('Error cleaning up services: $e');
    }
  }

  /// Check if all required services are initialized
  static bool areServicesInitialized() {
    return Get.isRegistered<PendingPhotoService>() &&
        Get.isRegistered<BackgroundUploadService>();
  }

  /// Get service status for debugging
  static Map<String, bool> getServiceStatus() {
    return {
      'PendingPhotoService': Get.isRegistered<PendingPhotoService>(),
      'BackgroundUploadService': Get.isRegistered<BackgroundUploadService>(),
    };
  }

  /// Debug method to check and print service status
  static void debugServiceStatus() {
    print('DEBUG: === SERVICE STATUS ===');
    print(
      'DEBUG: PendingPhotoService registered: ${Get.isRegistered<PendingPhotoService>()}',
    );
    print(
      'DEBUG: BackgroundUploadService registered: ${Get.isRegistered<BackgroundUploadService>()}',
    );

    try {
      final pendingService = Get.find<PendingPhotoService>();
      print(
        'DEBUG: PendingPhotoService found - pending photos: ${pendingService.pendingPhotos.length}',
      );
    } catch (e) {
      print('DEBUG: PendingPhotoService not found: $e');
    }

    try {
      final uploadService = Get.find<BackgroundUploadService>();
      print(
        'DEBUG: BackgroundUploadService found - active: ${uploadService.isBackgroundUploadActive}',
      );
      print(
        'DEBUG: BackgroundUploadService - total pending: ${uploadService.totalPendingPhotos.value}',
      );
    } catch (e) {
      print('DEBUG: BackgroundUploadService not found: $e');
    }
    print('DEBUG: === END SERVICE STATUS ===');
  }
}
